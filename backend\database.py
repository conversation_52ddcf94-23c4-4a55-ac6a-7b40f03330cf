# This file is deprecated. Use models/track.py instead.
# Keeping for backward compatibility during migration.

import warnings
from .models.track import Track, DatabaseManager
from .config import config

warnings.warn(
    "database.py is deprecated. Use models/track.py instead.",
    DeprecationWarning,
    stacklevel=2
)

# Legacy compatibility
def save_track(filename, metadata, stems_path):
    """
    Legacy function for saving tracks.
    This is deprecated - use the new Track model and DatabaseManager instead.
    """
    warnings.warn(
        "save_track function is deprecated. Use Track model directly.",
        DeprecationWarning,
        stacklevel=2
    )

    # Use new database manager
    db_manager = DatabaseManager(config['default'].DATABASE_URL)

    with db_manager.get_session() as session:
        try:
            track = Track(
                filename=filename,
                original_filename=filename,  # Assume same for legacy
                bpm=metadata.get('bpm'),
                key=metadata.get('key'),
                genre=metadata.get('genre'),
                stems_path=stems_path,
                duration=metadata.get('duration'),
                processing_status='completed'
            )
            session.add(track)
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()