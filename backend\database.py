from sqlalchemy import create_engine, Column, Integer, String, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

engine = create_engine('sqlite:///db/music.db')  # Replace with PostgreSQL later
Base = declarative_base()
Session = sessionmaker(bind=engine)
session = Session()

class Track(Base):
    __tablename__ = 'tracks'
    id = Column(Integer, primary_key=True)
    filename = Column(String)
    bpm = Column(Integer)
    key = Column(String)
    genre = Column(String)
    stems_path = Column(String)

Base.metadata.create_all(engine)

def save_track(filename, metadata, stems_path):
    track = Track(
        filename=filename,
        bpm=metadata['bpm'],
        key=metadata['key'],
        genre=metadata['genre'],
        stems_path=metadata['stems_path']
    )
    session.add(track)
    session.commit()