"""
Music Processing API without audio processing dependencies
Use this version if you're having Spleeter/Typer dependency conflicts
"""

import os
import logging
from flask import Flask, request, jsonify, abort
from flask_cors import CORS
from contextlib import contextmanager

# Configure for CPU-only usage (suppress GPU warnings)
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

try:
    # Import configuration
    from .config import config
    
    # Import models
    from .models.track import Track, DatabaseManager
    
    # Import utilities
    from .utils.validators import (
        validate_file_upload, 
        track_response_schema,
        error_response_schema,
        success_response_schema
    )
except ImportError:
    # Fallback configuration if imports fail
    class SimpleConfig:
        DEBUG = True
        UPLOAD_FOLDER = 'uploads'
        STEMS_FOLDER = 'stems'
        MAX_CONTENT_LENGTH = 50 * 1024 * 1024
        ALLOWED_EXTENSIONS = {'mp3', 'wav', 'flac', 'ogg', 'm4a', 'aac'}
        DATABASE_URL = 'sqlite:///db/music.db'
        CORS_ORIGINS = ['*']
        SECRET_KEY = 'dev-secret-key'
    
    config = {'default': SimpleConfig}
    Track = None
    DatabaseManager = None
    validate_file_upload = None
    track_response_schema = None
    error_response_schema = None
    success_response_schema = None

def create_app(config_name='default'):
    """Application factory pattern."""
    
    # Create Flask app
    app = Flask(__name__)
    
    # Load configuration
    if config and config_name in config:
        app.config.from_object(config[config_name])
    else:
        # Fallback configuration
        app.config.update({
            'DEBUG': True,
            'UPLOAD_FOLDER': 'uploads',
            'STEMS_FOLDER': 'stems',
            'MAX_CONTENT_LENGTH': 50 * 1024 * 1024,
            'ALLOWED_EXTENSIONS': {'mp3', 'wav', 'flac', 'ogg', 'm4a', 'aac'},
            'SECRET_KEY': 'dev-secret-key'
        })
    
    # Setup CORS
    CORS(app, origins=['*'])
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Create directories
    os.makedirs(app.config.get('UPLOAD_FOLDER', 'uploads'), exist_ok=True)
    os.makedirs(app.config.get('STEMS_FOLDER', 'stems'), exist_ok=True)
    os.makedirs('db', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    def allowed_file(filename):
        """Check if file extension is allowed."""
        return ('.' in filename and 
                filename.rsplit('.', 1)[1].lower() in app.config.get('ALLOWED_EXTENSIONS', set()))
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle bad request errors."""
        return jsonify({
            'error': 'Bad Request',
            'message': str(error.description),
            'status_code': 400
        }), 400
    
    @app.errorhandler(404)
    def not_found(error):
        """Handle not found errors."""
        return jsonify({
            'error': 'Not Found',
            'message': 'Resource not found',
            'status_code': 404
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle internal server errors."""
        app.logger.error(f"Internal server error: {str(error)}")
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred',
            'status_code': 500
        }), 500
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint."""
        return jsonify({
            'status': 'healthy',
            'message': 'Music processing API is running (no audio processing mode)',
            'mode': 'no_audio_processing'
        })
    
    @app.route('/upload', methods=['POST'])
    def upload_audio():
        """Upload endpoint without audio processing."""
        try:
            # Validate request
            if 'audio' not in request.files:
                abort(400, 'No audio file provided')
            
            file = request.files['audio']
            
            # Basic file validation
            if not file or not file.filename:
                abort(400, 'No file selected')
            
            if not allowed_file(file.filename):
                abort(400, f'File type not allowed. Allowed types: {app.config["ALLOWED_EXTENSIONS"]}')
            
            # Check file size
            if request.content_length and request.content_length > app.config['MAX_CONTENT_LENGTH']:
                abort(400, f'File too large. Maximum size: {app.config["MAX_CONTENT_LENGTH"]} bytes')
            
            # For now, just return success without actual processing
            return jsonify({
                'status': 'success',
                'message': 'File uploaded successfully (audio processing disabled)',
                'data': {
                    'filename': file.filename,
                    'note': 'Audio processing is disabled in this mode. Install audio dependencies to enable processing.',
                    'mode': 'no_audio_processing'
                }
            })
            
        except Exception as e:
            app.logger.error(f"Upload error: {str(e)}")
            return jsonify({'error': 'Upload failed', 'message': str(e)}), 500
    
    @app.route('/status', methods=['GET'])
    def status():
        """Status endpoint showing available features."""
        return jsonify({
            'status': 'running',
            'mode': 'no_audio_processing',
            'features': {
                'file_upload': True,
                'audio_processing': False,
                'stem_separation': False,
                'metadata_extraction': False
            },
            'message': 'Audio processing disabled. Install Spleeter and librosa to enable full functionality.'
        })
    
    return app

# Create app instance
app = create_app(os.environ.get('FLASK_ENV', 'default'))

if __name__ == '__main__':
    print("🎵 Starting Music Processing API (No Audio Processing Mode)")
    print("=" * 60)
    print("This version runs without Spleeter/audio processing dependencies.")
    print("Perfect for testing Flask setup and resolving dependency conflicts.")
    print("=" * 60)
    
    app.run(
        debug=app.config.get('DEBUG', True),
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000))
    )
