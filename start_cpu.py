#!/usr/bin/env python3
"""
CPU-optimized startup script for Music Processing API
"""

import os
import sys

def setup_cpu_environment():
    """Setup environment variables for optimal CPU performance."""
    
    print("🖥️  Configuring for CPU-only processing...")
    
    # TensorFlow CPU configuration
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # Suppress INFO and WARNING
    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # Force CPU usage
    os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'false'
    
    # NumPy/SciPy CPU optimization
    cpu_count = os.cpu_count()
    os.environ['OMP_NUM_THREADS'] = str(cpu_count)
    os.environ['MKL_NUM_THREADS'] = str(cpu_count)
    os.environ['NUMEXPR_NUM_THREADS'] = str(cpu_count)
    
    # Librosa optimization
    os.environ['LIBROSA_CACHE_DIR'] = os.path.join(os.getcwd(), '.librosa_cache')
    
    print(f"✅ Configured for {cpu_count} CPU cores")
    print("✅ GPU warnings suppressed")
    print("✅ CPU performance optimized")

def check_cpu_requirements():
    """Check if CPU-optimized packages are available."""
    
    print("\n🔍 Checking CPU-optimized packages...")
    
    required_packages = [
        ('numpy', 'NumPy'),
        ('scipy', 'SciPy'),
        ('librosa', 'librosa'),
        ('flask', 'Flask'),
        ('tensorflow', 'TensorFlow')
    ]
    
    missing_packages = []
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✅ {name} available")
        except ImportError:
            print(f"❌ {name} missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements-cpu.txt")
        return False
    
    return True

def start_app():
    """Start the application with CPU optimizations."""
    
    print("\n🚀 Starting Music Processing API (CPU Mode)")
    print("=" * 50)
    
    # Import and start the app
    try:
        from backend.app import app
        
        print("🎵 Server starting on http://localhost:5000")
        print("📊 Health check: http://localhost:5000/health")
        print("📁 Upload endpoint: http://localhost:5000/upload")
        print("\n💡 Note: Processing will use CPU only (no GPU acceleration)")
        print("⏱️  Audio processing may take longer on CPU")
        print("\nPress Ctrl+C to stop the server")
        print("=" * 50)
        
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            threaded=True  # Enable threading for better CPU utilization
        )
        
    except ImportError as e:
        print(f"❌ Failed to import app: {e}")
        print("\nTry running the simple version first:")
        print("   python backend/app_simple.py")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)

def main():
    """Main function."""
    
    print("🎵 Music Processing API - CPU Mode")
    print("=" * 40)
    
    # Setup CPU environment
    setup_cpu_environment()
    
    # Check requirements
    if not check_cpu_requirements():
        print("\n💡 To install CPU-optimized packages:")
        print("   pip install -r requirements-cpu.txt")
        sys.exit(1)
    
    # Start the application
    start_app()

if __name__ == "__main__":
    main()
