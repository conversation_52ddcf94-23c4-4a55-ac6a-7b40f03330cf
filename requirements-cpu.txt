# CPU-optimized requirements for Music Processing API
# This version is optimized for systems without GPU

# Core Flask dependencies
Flask==2.3.3
flask-cors==4.0.0
Werkzeug==2.3.7

# Database
SQLAlchemy==2.0.23

# Configuration and validation
python-dotenv==1.0.0
marshmallow==3.20.1

# Utilities
click>=8.0.0

# Audio processing - CPU optimized versions
numpy==1.24.3
scipy==1.10.1

# TensorFlow CPU-only (smaller, faster for CPU)
tensorflow-cpu==2.9.3

# Audio processing libraries
librosa==0.10.1
spleeter==2.4.0
ffmpeg-python==0.2.0

# Audio format support
soundfile>=0.10.0
audioread>=2.1.0

# Production server
gunicorn==21.2.0

# Optional: Performance monitoring
psutil>=5.8.0
