import os
import shutil
import logging
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage

logger = logging.getLogger(__name__)

class FileStorageService:
    """Service for handling file storage operations."""
    
    def __init__(self, upload_folder: str, stems_folder: str, allowed_extensions: set):
        """
        Initialize the file storage service.
        
        Args:
            upload_folder: Directory for uploaded files
            stems_folder: Directory for separated stems
            allowed_extensions: Set of allowed file extensions
        """
        self.upload_folder = upload_folder
        self.stems_folder = stems_folder
        self.allowed_extensions = allowed_extensions
        
        # Create directories if they don't exist
        os.makedirs(upload_folder, exist_ok=True)
        os.makedirs(stems_folder, exist_ok=True)
    
    def is_allowed_file(self, filename: str) -> bool:
        """
        Check if file extension is allowed.
        
        Args:
            filename: Name of the file
            
        Returns:
            True if file extension is allowed, False otherwise
        """
        return ('.' in filename and 
                filename.rsplit('.', 1)[1].lower() in self.allowed_extensions)
    
    def save_uploaded_file(self, file: FileStorage) -> Tuple[str, str]:
        """
        Save uploaded file to disk.
        
        Args:
            file: Uploaded file object
            
        Returns:
            Tuple of (secure_filename, full_file_path)
            
        Raises:
            ValueError: If file is invalid or extension not allowed
            Exception: If file save fails
        """
        if not file or not file.filename:
            raise ValueError("No file provided")
            
        if not self.is_allowed_file(file.filename):
            raise ValueError(f"File type not allowed. Allowed types: {self.allowed_extensions}")
        
        # Generate secure filename
        original_filename = file.filename
        secure_name = secure_filename(original_filename)
        
        # Handle duplicate filenames
        counter = 1
        base_name, ext = os.path.splitext(secure_name)
        final_filename = secure_name
        
        while os.path.exists(os.path.join(self.upload_folder, final_filename)):
            final_filename = f"{base_name}_{counter}{ext}"
            counter += 1
        
        # Save file
        file_path = os.path.join(self.upload_folder, final_filename)
        
        try:
            file.save(file_path)
            logger.info(f"File saved: {file_path}")
            return final_filename, file_path
            
        except Exception as e:
            logger.error(f"Failed to save file {final_filename}: {str(e)}")
            raise Exception(f"Failed to save file: {str(e)}")
    
    def get_file_size(self, file_path: str) -> int:
        """
        Get file size in bytes.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File size in bytes
        """
        try:
            return os.path.getsize(file_path)
        except OSError:
            return 0
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete a file from disk.
        
        Args:
            file_path: Path to the file to delete
            
        Returns:
            True if file was deleted, False otherwise
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"File deleted: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {str(e)}")
            return False
    
    def delete_directory(self, dir_path: str) -> bool:
        """
        Delete a directory and all its contents.
        
        Args:
            dir_path: Path to the directory to delete
            
        Returns:
            True if directory was deleted, False otherwise
        """
        try:
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
                logger.info(f"Directory deleted: {dir_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete directory {dir_path}: {str(e)}")
            return False
    
    def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up old files and directories.
        
        Args:
            max_age_hours: Maximum age of files in hours
            
        Returns:
            Number of files/directories cleaned up
        """
        import time
        
        cleanup_count = 0
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        # Clean up upload folder
        for filename in os.listdir(self.upload_folder):
            file_path = os.path.join(self.upload_folder, filename)
            if os.path.isfile(file_path):
                file_age = current_time - os.path.getctime(file_path)
                if file_age > max_age_seconds:
                    if self.delete_file(file_path):
                        cleanup_count += 1
        
        # Clean up stems folder
        for dirname in os.listdir(self.stems_folder):
            dir_path = os.path.join(self.stems_folder, dirname)
            if os.path.isdir(dir_path):
                dir_age = current_time - os.path.getctime(dir_path)
                if dir_age > max_age_seconds:
                    if self.delete_directory(dir_path):
                        cleanup_count += 1
        
        logger.info(f"Cleanup completed. Removed {cleanup_count} items.")
        return cleanup_count
