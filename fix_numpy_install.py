#!/usr/bin/env python3
"""
Fix NumPy installation issues on Windows
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        if e.stderr:
            print(f"Error: {e.stderr.strip()}")
        if e.stdout:
            print(f"Output: {e.stdout.strip()}")
        return False

def fix_numpy_installation():
    """Fix NumPy installation issues."""
    print("🔧 Fixing NumPy Installation Issues")
    print("=" * 50)
    
    # Step 1: Upgrade pip, setuptools, and wheel
    print("\n📦 Step 1: Upgrading build tools...")
    commands = [
        ("python -m pip install --upgrade pip", "Upgrading pip"),
        ("pip install --upgrade setuptools", "Upgrading setuptools"),
        ("pip install --upgrade wheel", "Upgrading wheel"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            print(f"⚠️  Warning: {description} failed, continuing...")
    
    # Step 2: Install NumPy with specific options
    print("\n🔢 Step 2: Installing NumPy...")
    numpy_commands = [
        # Try installing from pre-compiled wheel first
        ("pip install --only-binary=all numpy==1.24.3", "Installing NumPy from wheel"),
        # If that fails, try without build isolation
        ("pip install --no-build-isolation numpy==1.24.3", "Installing NumPy without build isolation"),
        # Last resort: force reinstall
        ("pip install --force-reinstall --no-deps numpy==1.24.3", "Force reinstalling NumPy"),
    ]
    
    numpy_installed = False
    for command, description in numpy_commands:
        if run_command(command, description):
            numpy_installed = True
            break
        print(f"⚠️  {description} failed, trying next method...")
    
    if not numpy_installed:
        print("❌ All NumPy installation methods failed!")
        return False
    
    # Step 3: Install other dependencies
    print("\n📚 Step 3: Installing other dependencies...")
    
    # Install core dependencies first
    core_deps = [
        "scipy==1.10.1",
        "librosa==0.10.1",
        "Flask==2.3.3",
        "flask-cors==4.0.0",
        "SQLAlchemy==2.0.23",
        "marshmallow==3.20.1",
        "python-dotenv==1.0.0",
        "Werkzeug==3.1.3"
    ]
    
    for dep in core_deps:
        run_command(f"pip install {dep}", f"Installing {dep}")
    
    # Step 4: Install remaining dependencies
    print("\n🎵 Step 4: Installing audio processing dependencies...")
    audio_deps = [
        "spleeter==2.4.0",
        "ffmpeg-python==0.2.0"
    ]
    
    for dep in audio_deps:
        run_command(f"pip install {dep}", f"Installing {dep}")
    
    print("\n✅ Installation completed!")
    return True

def verify_installation():
    """Verify that key packages are installed correctly."""
    print("\n🧪 Verifying installation...")
    
    test_imports = [
        ("numpy", "NumPy"),
        ("scipy", "SciPy"),
        ("librosa", "librosa"),
        ("flask", "Flask"),
        ("spleeter", "Spleeter")
    ]
    
    all_good = True
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name} imported successfully")
        except ImportError as e:
            print(f"❌ {name} import failed: {e}")
            all_good = False
    
    return all_good

def main():
    """Main function."""
    print("🎵 NumPy Installation Fix for Music Processing API")
    print("=" * 60)
    
    # Check Python version
    print(f"🐍 Python version: {sys.version}")
    
    if fix_numpy_installation():
        if verify_installation():
            print("\n🎉 All packages installed and verified successfully!")
            print("\nNext steps:")
            print("1. Try running: python backend/app.py")
            print("2. If you still have issues, try the alternative solutions below")
        else:
            print("\n⚠️  Some packages failed to import. See alternative solutions below.")
    else:
        print("\n❌ Installation failed. See alternative solutions below.")
    
    print("\n" + "=" * 60)
    print("🔧 ALTERNATIVE SOLUTIONS:")
    print("=" * 60)
    print("\n1. Use Conda instead of pip:")
    print("   conda create -n music-env python=3.9")
    print("   conda activate music-env")
    print("   conda install numpy scipy librosa flask")
    print("   pip install spleeter flask-cors sqlalchemy marshmallow python-dotenv")
    
    print("\n2. Use pre-compiled packages:")
    print("   pip install --find-links https://download.pytorch.org/whl/torch_stable.html numpy")
    
    print("\n3. Install Visual Studio Build Tools:")
    print("   Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/")
    print("   Install 'C++ build tools' workload")
    
    print("\n4. Use Windows Subsystem for Linux (WSL):")
    print("   Install WSL2 and run the project in a Linux environment")

if __name__ == "__main__":
    main()
