import librosa
import numpy as np
import logging
from typing import Dict, Optional, Tuple
import os

logger = logging.getLogger(__name__)

class AudioMetadataService:
    """Service for extracting metadata from audio files."""
    
    def __init__(self):
        """Initialize the audio metadata service."""
        pass
    
    def extract_metadata(self, audio_path: str, stems_folder: Optional[str] = None) -> Dict:
        """
        Extract comprehensive metadata from an audio file.
        
        Args:
            audio_path: Path to the audio file
            stems_folder: Path to separated stems folder (optional)
            
        Returns:
            Dictionary containing extracted metadata
            
        Raises:
            FileNotFoundError: If audio file doesn't exist
            Exception: If metadata extraction fails
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
            
        try:
            logger.info(f"Extracting metadata from: {audio_path}")
            
            # Load audio file
            y, sr = librosa.load(audio_path)
            duration = librosa.get_duration(y=y, sr=sr)
            
            # Extract basic features
            tempo = self._extract_tempo(y, sr)
            key = self._extract_key(y, sr)
            genre = self._classify_genre(y, sr)
            
            # Extract additional features
            spectral_features = self._extract_spectral_features(y, sr)
            
            metadata = {
                "duration": round(duration, 2),
                "bpm": round(tempo) if tempo else None,
                "key": key,
                "genre": genre,
                "stems_path": stems_folder,
                **spectral_features
            }
            
            logger.info(f"Metadata extraction completed for: {audio_path}")
            return metadata
            
        except Exception as e:
            logger.error(f"Metadata extraction failed for {audio_path}: {str(e)}")
            raise Exception(f"Metadata extraction failed: {str(e)}")
    
    def _extract_tempo(self, y: np.ndarray, sr: int) -> Optional[float]:
        """Extract tempo (BPM) from audio."""
        try:
            tempo, _ = librosa.beat.beat_track(y=y, sr=sr)
            return float(tempo)
        except Exception as e:
            logger.warning(f"Tempo extraction failed: {str(e)}")
            return None
    
    def _extract_key(self, y: np.ndarray, sr: int) -> Optional[str]:
        """Extract musical key from audio."""
        try:
            # Extract chroma features
            chroma = librosa.feature.chroma_cqt(y=y, sr=sr)
            
            # Get the most prominent chroma class
            chroma_mean = np.mean(chroma, axis=1)
            key_index = np.argmax(chroma_mean)
            
            # Map to note names
            notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
            return notes[key_index]
            
        except Exception as e:
            logger.warning(f"Key extraction failed: {str(e)}")
            return None
    
    def _classify_genre(self, y: np.ndarray, sr: int) -> str:
        """
        Classify genre based on audio features.
        This is a placeholder implementation - in production, you'd use a trained model.
        """
        try:
            # Extract features for genre classification
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            spectral_centroid = librosa.feature.spectral_centroid(y=y, sr=sr)
            zero_crossing_rate = librosa.feature.zero_crossing_rate(y)
            
            # Simple heuristic-based classification (placeholder)
            avg_mfcc = np.mean(mfccs)
            avg_spectral_centroid = np.mean(spectral_centroid)
            avg_zcr = np.mean(zero_crossing_rate)
            
            # Basic genre classification based on features
            if avg_spectral_centroid > 3000 and avg_zcr > 0.1:
                return "Electronic"
            elif avg_spectral_centroid < 1500:
                return "Classical"
            elif avg_mfcc > 0:
                return "Pop"
            else:
                return "Rock"
                
        except Exception as e:
            logger.warning(f"Genre classification failed: {str(e)}")
            return "Unknown"
    
    def _extract_spectral_features(self, y: np.ndarray, sr: int) -> Dict:
        """Extract additional spectral features."""
        try:
            features = {}
            
            # Spectral centroid
            spectral_centroid = librosa.feature.spectral_centroid(y=y, sr=sr)
            features['spectral_centroid'] = float(np.mean(spectral_centroid))
            
            # Spectral rolloff
            spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)
            features['spectral_rolloff'] = float(np.mean(spectral_rolloff))
            
            # Zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(y)
            features['zero_crossing_rate'] = float(np.mean(zcr))
            
            # RMS energy
            rms = librosa.feature.rms(y=y)
            features['rms_energy'] = float(np.mean(rms))
            
            return features
            
        except Exception as e:
            logger.warning(f"Spectral feature extraction failed: {str(e)}")
            return {}

# Global instance
metadata_service = AudioMetadataService()

def extract_metadata(audio_path: str, stems_folder: Optional[str] = None) -> Dict:
    """
    Convenience function for metadata extraction.
    
    Args:
        audio_path: Path to the audio file
        stems_folder: Path to separated stems folder (optional)
        
    Returns:
        Dictionary containing extracted metadata
    """
    return metadata_service.extract_metadata(audio_path, stems_folder)
