@echo off
echo 🎵 Music Processing API - CPU Mode
echo =====================================

echo.
echo 🖥️  Configuring for CPU-only processing...

REM Set environment variables for CPU optimization
set TF_CPP_MIN_LOG_LEVEL=2
set CUDA_VISIBLE_DEVICES=-1
set TF_FORCE_GPU_ALLOW_GROWTH=false

REM Set CPU thread optimization
for /f %%i in ('echo %NUMBER_OF_PROCESSORS%') do set CPU_COUNT=%%i
set OMP_NUM_THREADS=%CPU_COUNT%
set MKL_NUM_THREADS=%CPU_COUNT%
set NUMEXPR_NUM_THREADS=%CPU_COUNT%

echo ✅ Configured for %CPU_COUNT% CPU cores
echo ✅ GPU warnings suppressed
echo ✅ CPU performance optimized

echo.
echo 🚀 Starting Music Processing API...
echo =====================================
echo 🎵 Server will start on http://localhost:5000
echo 📊 Health check: http://localhost:5000/health
echo 📁 Upload endpoint: http://localhost:5000/upload
echo.
echo 💡 Note: Processing will use CPU only (no GPU acceleration)
echo ⏱️  Audio processing may take longer on CPU
echo.
echo Press Ctrl+C to stop the server
echo =====================================

python start_cpu.py

pause
