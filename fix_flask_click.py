#!/usr/bin/env python3
"""
Fix Flask and Click compatibility issues
"""

import subprocess
import sys

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False

def fix_flask_click_compatibility():
    """Fix Flask and Click compatibility issues."""
    print("🔧 Fixing Flask and Click Compatibility")
    print("=" * 45)
    
    # Step 1: Uninstall conflicting packages
    print("\n🗑️  Step 1: Removing conflicting packages...")
    uninstall_commands = [
        ("pip uninstall -y flask", "Uninstalling Flask"),
        ("pip uninstall -y click", "Uninstalling Click"),
        ("pip uninstall -y werkzeug", "Uninstalling Werkzeug"),
    ]
    
    for command, description in uninstall_commands:
        run_command(command, description)
    
    # Step 2: Install compatible versions
    print("\n📦 Step 2: Installing compatible versions...")
    install_commands = [
        ("pip install click>=8.0.0", "Installing Click 8.0+"),
        ("pip install Werkzeug==2.3.7", "Installing compatible Werkzeug"),
        ("pip install Flask==2.3.3", "Installing Flask 2.3.3"),
        ("pip install flask-cors==4.0.0", "Installing Flask-CORS"),
    ]
    
    for command, description in install_commands:
        if not run_command(command, description):
            print(f"⚠️  {description} failed, trying alternative...")
            # Try without version constraints
            package = command.split()[2].split('>=')[0].split('==')[0]
            alt_command = f"pip install {package}"
            run_command(alt_command, f"Installing {package} (latest)")
    
    # Step 3: Verify installation
    print("\n🧪 Step 3: Verifying installation...")
    try:
        import flask
        import click
        print(f"✅ Flask version: {flask.__version__}")
        print(f"✅ Click version: {click.__version__}")
        
        # Test the specific import that was failing
        from click.core import ParameterSource
        print("✅ ParameterSource import successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """Main function."""
    print("🎵 Flask/Click Compatibility Fix")
    print("=" * 35)
    
    if fix_flask_click_compatibility():
        print("\n🎉 Flask and Click compatibility fixed!")
        print("\nYou can now try running:")
        print("   python backend/app.py")
    else:
        print("\n❌ Fix failed. Try the manual steps below:")
        print("\n🔧 Manual Fix Steps:")
        print("1. pip uninstall flask click werkzeug")
        print("2. pip install click>=8.0.0")
        print("3. pip install Flask==2.3.3")
        print("4. pip install flask-cors==4.0.0")
        print("\nOr try using a different Flask version:")
        print("   pip install Flask==2.2.5")

if __name__ == "__main__":
    main()
