@echo off
echo 🎵 Music Processing API - NumPy Installation Fix
echo ================================================

echo.
echo 🔄 Step 1: Upgrading pip and build tools...
python -m pip install --upgrade pip
pip install --upgrade setuptools wheel

echo.
echo 🔄 Step 2: Installing NumPy from pre-compiled wheel...
pip install --only-binary=all numpy==1.24.3

if %errorlevel% neq 0 (
    echo ⚠️  Wheel installation failed, trying without build isolation...
    pip install --no-build-isolation numpy==1.24.3
)

if %errorlevel% neq 0 (
    echo ⚠️  Still failing, trying force reinstall...
    pip install --force-reinstall --no-deps numpy==1.24.3
)

echo.
echo 🔄 Step 3: Installing core dependencies...
pip install scipy==1.10.1
pip install Flask==2.3.3
pip install flask-cors==4.0.0
pip install SQLAlchemy==2.0.23
pip install marshmallow==3.20.1
pip install python-dotenv==1.0.0
pip install Werkzeug==3.1.3

echo.
echo 🔄 Step 4: Installing audio processing libraries...
pip install librosa==0.10.1
pip install spleeter==2.4.0
pip install ffmpeg-python==0.2.0

echo.
echo 🔄 Step 5: Installing remaining dependencies...
pip install click==7.1.2
pip install gunicorn==21.2.0

echo.
echo ✅ Installation completed!
echo.
echo 🧪 Testing imports...
python -c "import numpy; print('✅ NumPy:', numpy.__version__)"
python -c "import scipy; print('✅ SciPy:', scipy.__version__)"
python -c "import librosa; print('✅ librosa:', librosa.__version__)"
python -c "import flask; print('✅ Flask:', flask.__version__)"

echo.
echo 🎉 Setup complete! You can now run:
echo    python backend/app.py
echo.
pause
