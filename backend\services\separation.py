import os
import logging
from spleeter.separator import Separator
from typing import Optional

logger = logging.getLogger(__name__)

class StemSeparationService:
    """Service for separating audio into stems using Spleeter."""
    
    def __init__(self, model_name: str = 'spleeter:5stems'):
        """
        Initialize the stem separation service.
        
        Args:
            model_name: Spleeter model to use (e.g., 'spleeter:5stems')
        """
        self.model_name = model_name
        self._separator = None
        
    @property
    def separator(self):
        """Lazy load the separator to avoid loading on import."""
        if self._separator is None:
            try:
                self._separator = Separator(self.model_name)
                logger.info(f"Loaded Spleeter model: {self.model_name}")
            except Exception as e:
                logger.error(f"Failed to load Spleeter model {self.model_name}: {str(e)}")
                raise
        return self._separator
    
    def separate_stems(self, audio_path: str, output_dir: Optional[str] = None) -> str:
        """
        Separate audio file into stems.
        
        Args:
            audio_path: Path to the input audio file
            output_dir: Output directory for stems (optional)
            
        Returns:
            Path to the directory containing separated stems
            
        Raises:
            FileNotFoundError: If input audio file doesn't exist
            Exception: If separation fails
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
            
        if output_dir is None:
            # Create output directory based on input filename
            base_name = os.path.splitext(os.path.basename(audio_path))[0]
            output_dir = f'stems/{base_name}'
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            logger.info(f"Starting stem separation for: {audio_path}")
            self.separator.separate_to_file(audio_path, output_dir)
            logger.info(f"Stem separation completed. Output: {output_dir}")
            return output_dir
            
        except Exception as e:
            logger.error(f"Stem separation failed for {audio_path}: {str(e)}")
            raise Exception(f"Stem separation failed: {str(e)}")
    
    def get_stem_files(self, stems_dir: str) -> dict:
        """
        Get paths to individual stem files.
        
        Args:
            stems_dir: Directory containing separated stems
            
        Returns:
            Dictionary mapping stem names to file paths
        """
        stem_files = {}
        
        if not os.path.exists(stems_dir):
            return stem_files
            
        # Common stem names for 5-stem model
        stem_names = ['vocals', 'drums', 'bass', 'piano', 'other']
        
        for stem_name in stem_names:
            stem_file = os.path.join(stems_dir, f"{stem_name}.wav")
            if os.path.exists(stem_file):
                stem_files[stem_name] = stem_file
                
        return stem_files

# Global instance
stem_service = StemSeparationService()

def separate_stems(audio_path: str, output_dir: Optional[str] = None) -> str:
    """
    Convenience function for stem separation.
    
    Args:
        audio_path: Path to the input audio file
        output_dir: Output directory for stems (optional)
        
    Returns:
        Path to the directory containing separated stems
    """
    return stem_service.separate_stems(audio_path, output_dir)
