#!/usr/bin/env python3
"""
Fix dependency conflicts between Flask, Click, Typer, and Spleeter
"""

import subprocess
import sys

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False

def fix_dependency_conflicts():
    """Fix Flask/Click/Typer dependency conflicts."""
    print("🔧 Fixing Dependency Conflicts")
    print("=" * 35)
    
    print("\n📋 The Issue:")
    print("- Flask 2.3.3 needs Click ≥ 8.0.0")
    print("- Typer 0.3.2 needs Click < 7.2.0")
    print("- Spleeter depends on Typer 0.3.2")
    print("\n🔧 Solution: Use compatible versions")
    
    # Step 1: Clean slate
    print("\n🗑️  Step 1: Removing conflicting packages...")
    uninstall_commands = [
        ("pip uninstall -y flask", "Uninstalling Flask"),
        ("pip uninstall -y click", "Uninstalling Click"),
        ("pip uninstall -y werkzeug", "Uninstalling Werkzeug"),
        ("pip uninstall -y typer", "Uninstalling Typer"),
        ("pip uninstall -y flask-cors", "Uninstalling Flask-CORS"),
    ]
    
    for command, description in uninstall_commands:
        run_command(command, description)
    
    # Step 2: Install compatible versions
    print("\n📦 Step 2: Installing compatible versions...")
    
    # Method 1: Try with newer Typer that supports Click 8+
    print("\n🔄 Trying Method 1: Newer Typer + Flask 2.3.3...")
    method1_commands = [
        ("pip install 'click>=8.0.0'", "Installing Click 8+"),
        ("pip install 'typer>=0.9.0'", "Installing newer Typer"),
        ("pip install Werkzeug==2.3.7", "Installing Werkzeug"),
        ("pip install Flask==2.3.3", "Installing Flask 2.3.3"),
        ("pip install flask-cors==4.0.0", "Installing Flask-CORS"),
    ]
    
    method1_success = True
    for command, description in method1_commands:
        if not run_command(command, description):
            method1_success = False
            break
    
    if method1_success:
        # Test if Spleeter still works
        try:
            print("🧪 Testing Spleeter compatibility...")
            result = subprocess.run([sys.executable, "-c", "import spleeter; print('Spleeter OK')"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ Method 1 successful! All packages compatible.")
                return True
            else:
                print("⚠️  Spleeter compatibility issue, trying Method 2...")
        except Exception:
            print("⚠️  Spleeter test failed, trying Method 2...")
    
    # Method 2: Use older Flask that works with Click 7.x
    print("\n🔄 Trying Method 2: Older Flask + Click 7.x...")
    
    # Clean up first
    for command, description in uninstall_commands:
        run_command(command, description)
    
    method2_commands = [
        ("pip install 'click>=7.1.1,<7.2.0'", "Installing Click 7.x"),
        ("pip install 'typer==0.3.2'", "Installing Typer 0.3.2"),
        ("pip install Werkzeug==2.2.6", "Installing compatible Werkzeug"),
        ("pip install Flask==2.2.5", "Installing Flask 2.2.5"),
        ("pip install flask-cors==3.0.10", "Installing Flask-CORS"),
    ]
    
    method2_success = True
    for command, description in method2_commands:
        if not run_command(command, description):
            method2_success = False
            break
    
    if method2_success:
        print("✅ Method 2 successful! Using older compatible versions.")
        return True
    
    # Method 3: Minimal Flask without Spleeter
    print("\n🔄 Trying Method 3: Minimal Flask setup...")
    method3_commands = [
        ("pip install 'click>=8.0.0'", "Installing Click 8+"),
        ("pip install Werkzeug==2.3.7", "Installing Werkzeug"),
        ("pip install Flask==2.3.3", "Installing Flask 2.3.3"),
        ("pip install flask-cors==4.0.0", "Installing Flask-CORS"),
    ]
    
    for command, description in method3_commands:
        run_command(command, description)
    
    print("✅ Method 3: Basic Flask setup complete (Spleeter may need separate handling)")
    return True

def test_installation():
    """Test the installation."""
    print("\n🧪 Testing Installation...")
    
    test_imports = [
        ("flask", "Flask"),
        ("click", "Click"),
        ("werkzeug", "Werkzeug"),
    ]
    
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name} import successful")
        except ImportError as e:
            print(f"❌ {name} import failed: {e}")
    
    # Test the specific import that was failing
    try:
        from click.core import ParameterSource
        print("✅ ParameterSource import successful")
    except ImportError as e:
        print(f"❌ ParameterSource import failed: {e}")
    
    # Test Flask app creation
    try:
        from flask import Flask
        app = Flask(__name__)
        print("✅ Flask app creation successful")
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")

def main():
    """Main function."""
    print("🎵 Dependency Conflict Resolver")
    print("=" * 35)
    
    if fix_dependency_conflicts():
        test_installation()
        
        print("\n🎉 Dependency conflicts resolved!")
        print("\nNext steps:")
        print("1. Test with simple app: python backend/app_simple.py")
        print("2. If that works, try: python start_cpu.py")
        print("3. For audio processing, you may need to reinstall Spleeter:")
        print("   pip install spleeter==2.4.0")
        
        print("\n💡 If you still have Spleeter issues:")
        print("- Use the simple app for basic Flask functionality")
        print("- Install audio processing libraries separately")
        print("- Consider using a different virtual environment for Spleeter")
    else:
        print("\n❌ Could not resolve dependency conflicts automatically.")
        print("\n🔧 Manual steps to try:")
        print("1. Create a new virtual environment")
        print("2. Install packages in this order:")
        print("   pip install 'click>=7.1.1,<7.2.0'")
        print("   pip install Flask==2.2.5")
        print("   pip install flask-cors==3.0.10")
        print("   pip install spleeter==2.4.0")

if __name__ == "__main__":
    main()
