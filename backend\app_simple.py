"""
Simplified version of the Music Processing API
Use this if you're having compatibility issues with the main app.py
"""

import os
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS

# Simple configuration
class SimpleConfig:
    DEBUG = True
    UPLOAD_FOLDER = 'uploads'
    STEMS_FOLDER = 'stems'
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS = {'mp3', 'wav', 'flac', 'ogg', 'm4a', 'aac'}

def create_simple_app():
    """Create a simplified Flask app."""
    app = Flask(__name__)
    app.config.from_object(SimpleConfig)
    
    # Enable CORS
    CORS(app)
    
    # Create directories
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['STEMS_FOLDER'], exist_ok=True)
    
    def allowed_file(filename):
        """Check if file extension is allowed."""
        return ('.' in filename and 
                filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS'])
    
    @app.route('/', methods=['GET'])
    def root():
        """Root endpoint with API information."""
        return jsonify({
            'name': 'Music Processing API',
            'version': '1.0.0',
            'mode': 'simple',
            'status': 'running',
            'endpoints': {
                'health': '/health',
                'upload': '/upload (POST)',
                'status': '/status'
            },
            'message': 'Welcome to the Music Processing API (Simple Mode)'
        })

    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint."""
        return jsonify({
            'status': 'healthy',
            'message': 'Music processing API is running (simple mode)'
        })
    
    @app.route('/upload', methods=['POST'])
    def upload_audio():
        """Upload and process audio file (simplified version)."""
        try:
            # Check if file is present
            if 'audio' not in request.files:
                return jsonify({'error': 'No audio file provided'}), 400
            
            file = request.files['audio']
            
            # Check if file is selected
            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400
            
            # Check file extension
            if not allowed_file(file.filename):
                return jsonify({
                    'error': f'File type not allowed. Allowed types: {app.config["ALLOWED_EXTENSIONS"]}'
                }), 400
            
            # For now, just return a success message without actual processing
            # This allows you to test the API structure without audio processing dependencies
            return jsonify({
                'status': 'success',
                'message': 'File uploaded successfully (processing not implemented in simple mode)',
                'data': {
                    'filename': file.filename,
                    'note': 'This is a simplified version. Audio processing is not implemented.'
                }
            })
            
        except Exception as e:
            app.logger.error(f"Upload error: {str(e)}")
            return jsonify({'error': 'Upload failed'}), 500
    
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors."""
        return jsonify({'error': 'Not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors."""
        return jsonify({'error': 'Internal server error'}), 500
    
    return app

# Create app instance
app = create_simple_app()

if __name__ == '__main__':
    print("🎵 Starting Music Processing API (Simple Mode)")
    print("=" * 50)
    print("This is a simplified version that doesn't require audio processing libraries.")
    print("Use this to test the basic Flask setup and then upgrade to the full version.")
    print("=" * 50)
    
    app.run(
        debug=True,
        host='0.0.0.0',
        port=5000
    )
