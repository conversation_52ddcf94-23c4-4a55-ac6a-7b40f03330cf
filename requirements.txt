# Core Flask dependencies
Flask
flask-cors
Werkzeug

# Audio processing
spleeter
librosa
ffmpeg-python

# Database
SQLAlchemy

# Configuration and environment
python-dotenv

# Input validation and serialization
marshmallow

# Utilities
click

# Production server
gunicorn

# Testing dependencies (optional)
pytest
pytest-cov

# Development dependencies (optional)
black
flake8
mypy

# Core ML/Audio dependencies (from original requirements)
absl-py
anyio
astunparse
cachetools
certifi
charset-normalizer
exceptiongroup
flatbuffers
future
gast
google-auth
google-auth-oauthlib
google-pasta
grpcio
h11
h2
h5py
hpack
httpcore
httpx
hyperframe
idna
keras
Keras-Preprocessing
libclang
Markdown
MarkupSafe
norbert
numpy
oauthlib
opt_einsum
packaging
pandas
protobuf
pyasn1
pyasn1_modules
python-dateutil
pytz
requests
requests-oauthlib
rfc3986
rsa
scipy
six
sniffio
tensorboard
tensorboard-data-server
tensorboard-plugin-wit
tensorflow
tensorflow-estimator
tensorflow-io-gcs-filesystem
termcolor
typer
typing_extensions
urllib3
wrapt
