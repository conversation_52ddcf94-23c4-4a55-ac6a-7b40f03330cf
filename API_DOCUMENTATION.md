# Music Processing API Documentation

## Base URL
```
http://localhost:5000
```

## Authentication
Currently, no authentication is required. In production, consider implementing API keys or OAuth.

## Content Types
- Request: `multipart/form-data` for file uploads
- Response: `application/json`

## Error Handling

All errors return a JSON response with the following structure:

```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "status_code": 400
}
```

### Common HTTP Status Codes
- `200` - Success
- `400` - Bad Request (validation errors, missing files)
- `404` - Not Found
- `500` - Internal Server Error

## Endpoints

### Health Check

Check if the API is running and healthy.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "healthy",
  "message": "Music processing API is running"
}
```

**Example:**
```bash
curl -X GET http://localhost:5000/health
```

---

### Upload Audio File

Upload an audio file for processing (stem separation and metadata extraction).

**Endpoint:** `POST /upload`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `audio` (file, required): Audio file to process

**Supported File Formats:**
- MP3 (.mp3)
- WAV (.wav)
- FLAC (.flac)
- OGG (.ogg)
- M4A (.m4a)
- AAC (.aac)

**File Size Limit:** 50MB (configurable)

**Response:**
```json
{
  "status": "success",
  "message": "Audio processed successfully",
  "data": {
    "id": 1,
    "filename": "processed_song.mp3",
    "original_filename": "my_song.mp3",
    "file_size": 5242880,
    "duration": 180.5,
    "bpm": 120,
    "key": "C",
    "genre": "Pop",
    "stems_path": "stems/processed_song",
    "processing_status": "completed",
    "created_at": "2024-01-01T12:00:00.000Z",
    "updated_at": "2024-01-01T12:00:00.000Z",
    "spectral_centroid": 2500.0,
    "spectral_rolloff": 5000.0,
    "zero_crossing_rate": 0.1,
    "rms_energy": 0.05
  }
}
```

**Field Descriptions:**
- `id`: Unique identifier for the track
- `filename`: Secure filename used for storage
- `original_filename`: Original filename from upload
- `file_size`: File size in bytes
- `duration`: Track duration in seconds
- `bpm`: Beats per minute (tempo)
- `key`: Musical key (C, C#, D, etc.)
- `genre`: Classified genre
- `stems_path`: Path to separated audio stems
- `processing_status`: Status of processing (pending, processing, completed, failed)
- `spectral_centroid`: Average frequency weighted by magnitude
- `spectral_rolloff`: Frequency below which 85% of energy is contained
- `zero_crossing_rate`: Rate of sign changes in the signal
- `rms_energy`: Root mean square energy

**Example:**
```bash
curl -X POST \
  -F "audio=@/path/to/your/song.mp3" \
  http://localhost:5000/upload
```

**Error Responses:**

*No file provided:*
```json
{
  "error": "Bad Request",
  "message": "No audio file provided",
  "status_code": 400
}
```

*Invalid file format:*
```json
{
  "error": "Bad Request",
  "message": "File type not allowed. Allowed types: {'mp3', 'wav', 'flac', 'ogg', 'm4a', 'aac'}",
  "status_code": 400
}
```

*File too large:*
```json
{
  "error": "Bad Request",
  "message": "File too large. Maximum size: 52428800 bytes",
  "status_code": 400
}
```

*Processing failed:*
```json
{
  "error": "Internal Server Error",
  "message": "Audio processing failed: [specific error]",
  "status_code": 500
}
```

## Processing Details

### Stem Separation
The API uses Spleeter to separate audio into 5 stems:
- **vocals**: Lead and backing vocals
- **drums**: Drum kit and percussion
- **bass**: Bass guitar and low-frequency instruments
- **piano**: Piano and keyboard instruments
- **other**: All other instruments

Separated stems are saved as WAV files in the `stems/` directory.

### Metadata Extraction
The API extracts various audio features:
- **Tempo (BPM)**: Detected using beat tracking algorithms
- **Musical Key**: Estimated using chroma features
- **Genre**: Basic classification using spectral features
- **Spectral Features**: Various frequency-domain characteristics

## Rate Limiting

Currently, no rate limiting is implemented. Consider implementing rate limiting in production:
- Per-IP limits
- File size quotas
- Processing time limits

## CORS

Cross-Origin Resource Sharing (CORS) is enabled and configurable via the `CORS_ORIGINS` environment variable.

## Monitoring

### Logs
Application logs are written to:
- Console (development)
- File: `logs/app.log` (production)

### Health Monitoring
Use the `/health` endpoint for health checks and monitoring.

## Security Considerations

### Current Security Features
- File type validation
- Secure filename handling
- File size limits
- Input validation
- Error handling without information leakage

### Production Recommendations
- Implement authentication (API keys, OAuth)
- Add rate limiting
- Use HTTPS
- Implement request logging
- Add file content validation (not just extension)
- Consider virus scanning for uploaded files
- Implement user quotas

## Performance

### Processing Time
Processing time depends on:
- File size and duration
- Audio complexity
- Server resources

Typical processing times:
- 3-minute song: 30-60 seconds
- 5-minute song: 60-120 seconds

### Optimization Tips
- Use faster storage (SSD)
- Increase CPU cores for parallel processing
- Consider GPU acceleration for ML models
- Implement caching for repeated requests

## Examples

### Python Example
```python
import requests

# Upload file
with open('song.mp3', 'rb') as f:
    files = {'audio': f}
    response = requests.post('http://localhost:5000/upload', files=files)
    
if response.status_code == 200:
    data = response.json()
    print(f"Processing completed! Track ID: {data['data']['id']}")
    print(f"BPM: {data['data']['bpm']}")
    print(f"Key: {data['data']['key']}")
else:
    print(f"Error: {response.json()['message']}")
```

### JavaScript Example
```javascript
const formData = new FormData();
formData.append('audio', fileInput.files[0]);

fetch('http://localhost:5000/upload', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.status === 'success') {
        console.log('Processing completed!', data.data);
    } else {
        console.error('Error:', data.message);
    }
})
.catch(error => console.error('Error:', error));
```

### cURL Example
```bash
# Health check
curl http://localhost:5000/health

# Upload file
curl -X POST \
  -F "audio=@song.mp3" \
  -H "Accept: application/json" \
  http://localhost:5000/upload
```
