# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=true
SECRET_KEY=your-secret-key-here

# File Upload Settings
UPLOAD_FOLDER=uploads
STEMS_FOLDER=stems
MAX_CONTENT_LENGTH=52428800  # 50MB in bytes

# Database Configuration
DATABASE_URL=sqlite:///db/music.db
DEV_DATABASE_URL=sqlite:///db/music_dev.db

# Audio Processing Settings
SPLEETER_MODEL=spleeter:5stems

# CORS Settings (comma-separated origins)
CORS_ORIGINS=*

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Server Configuration
PORT=5000

# CPU Optimization (for systems without GPU)
TF_CPP_MIN_LOG_LEVEL=2
CUDA_VISIBLE_DEVICES=-1
OMP_NUM_THREADS=8
MKL_NUM_THREADS=8
NUMEXPR_NUM_THREADS=8
