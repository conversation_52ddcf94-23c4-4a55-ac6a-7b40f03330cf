# Music Processing API

A Flask-based REST API for audio processing that provides stem separation and metadata extraction capabilities using Spleeter and librosa.

## Features

- **Audio Upload**: Secure file upload with validation
- **Stem Separation**: Separate audio into 5 stems (vocals, drums, bass, piano, other) using Spleeter
- **Metadata Extraction**: Extract BPM, key, genre, and spectral features using librosa
- **Database Storage**: Track processing results and metadata
- **RESTful API**: Clean API endpoints with proper error handling
- **Security**: Input validation, secure file handling, and CORS support

## Project Structure

```
backend/
├── app.py              # Main Flask application
├── config.py           # Configuration management
├── models/
│   ├── __init__.py
│   └── track.py        # Database models
├── services/
│   ├── __init__.py
│   ├── separation.py   # Audio stem separation
│   ├── tagging.py      # Metadata extraction
│   └── storage.py      # File handling
├── utils/
│   ├── __init__.py
│   └── validators.py   # Input validation schemas
├── database.py         # Legacy database (deprecated)
├── separation.py       # Legacy separation (deprecated)
└── tagging.py          # Legacy tagging (deprecated)
```

## Installation

### Prerequisites

- Python 3.8+
- FFmpeg (required for audio processing)

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd music-project
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**

   For systems **with GPU**:
   ```bash
   pip install -r requirements.txt
   ```

   For systems **without GPU** (CPU-only, recommended):
   ```bash
   pip install -r requirements-cpu.txt
   ```

4. **Install FFmpeg**
   - **Windows**: Download from https://ffmpeg.org/download.html
   - **macOS**: `brew install ffmpeg`
   - **Linux**: `sudo apt-get install ffmpeg`

5. **Setup environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

6. **Create required directories**
   ```bash
   mkdir -p uploads stems db logs
   ```

## Usage

### Starting the Server

**For CPU-only systems (recommended):**
```bash
# Use the CPU-optimized startup script
python start_cpu.py

# Or on Windows
start_cpu.bat
```

**For systems with GPU:**
```bash
# Development mode
python backend/app.py

# Or with Flask CLI
export FLASK_APP=backend/app.py
flask run
```

**Simple mode (for testing):**
```bash
# If you're having dependency issues
python backend/app_simple.py
```

The API will be available at `http://localhost:5000`

### CPU vs GPU Performance

- **CPU Mode**: Recommended for most users. No GPU required.
  - Audio processing: 30-120 seconds per 3-minute song
  - Memory usage: 2-4 GB RAM
  - No CUDA warnings

- **GPU Mode**: Faster processing if you have a compatible NVIDIA GPU
  - Audio processing: 10-30 seconds per 3-minute song
  - Memory usage: 4-8 GB GPU VRAM + 2-4 GB RAM
  - Requires CUDA installation

### API Endpoints

#### Health Check
```http
GET /health
```

#### Upload Audio File
```http
POST /upload
Content-Type: multipart/form-data

{
  "audio": <audio_file>
}
```

**Supported formats**: MP3, WAV, FLAC, OGG, M4A, AAC

**Response**:
```json
{
  "status": "success",
  "message": "Audio processed successfully",
  "data": {
    "id": 1,
    "filename": "song.mp3",
    "original_filename": "my_song.mp3",
    "duration": 180.5,
    "bpm": 120,
    "key": "C",
    "genre": "Pop",
    "stems_path": "stems/song",
    "processing_status": "completed",
    "created_at": "2024-01-01T12:00:00",
    "spectral_centroid": 2500.0,
    "spectral_rolloff": 5000.0,
    "zero_crossing_rate": 0.1,
    "rms_energy": 0.05
  }
}
```

## Configuration

Configuration is managed through environment variables. See `.env.example` for all available options.

### Key Configuration Options

- `FLASK_ENV`: Environment (development/production)
- `SECRET_KEY`: Flask secret key (required for production)
- `MAX_CONTENT_LENGTH`: Maximum file upload size in bytes
- `DATABASE_URL`: Database connection string
- `SPLEETER_MODEL`: Spleeter model to use (spleeter:5stems)
- `CORS_ORIGINS`: Allowed CORS origins

## Development

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run tests
pytest

# Run with coverage
pytest --cov=backend
```

### Code Quality

```bash
# Install development dependencies
pip install black flake8 mypy

# Format code
black backend/

# Lint code
flake8 backend/

# Type checking
mypy backend/
```

## Security Features

- **File Validation**: Only allowed audio formats accepted
- **Secure Filenames**: Werkzeug secure_filename() used
- **Size Limits**: Configurable file size limits
- **Input Validation**: Marshmallow schemas for request validation
- **Error Handling**: Comprehensive error handling and logging
- **CORS**: Configurable CORS origins

## Performance Considerations

- **Lazy Loading**: Spleeter models loaded on first use
- **Session Management**: Proper database session handling
- **File Cleanup**: Automatic cleanup of old files
- **Logging**: Structured logging for monitoring

## Deployment

### Production Setup

1. **Set environment variables**
   ```bash
   export FLASK_ENV=production
   export SECRET_KEY=your-production-secret-key
   export DATABASE_URL=postgresql://user:pass@localhost/musicdb
   ```

2. **Use a production WSGI server**
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 backend.app:app
   ```

3. **Setup reverse proxy** (nginx recommended)

4. **Configure database** (PostgreSQL recommended for production)

### Docker Deployment

```dockerfile
FROM python:3.9-slim

# Install FFmpeg
RUN apt-get update && apt-get install -y ffmpeg

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "backend.app:app"]
```

## Troubleshooting

### Common Issues

1. **FFmpeg not found**
   - Ensure FFmpeg is installed and in PATH
   - On Windows, add FFmpeg to system PATH

2. **Spleeter model download fails**
   - Check internet connection
   - Models are downloaded on first use

3. **File upload fails**
   - Check file size limits
   - Verify file format is supported
   - Ensure upload directory exists and is writable

4. **Database errors**
   - Check database URL configuration
   - Ensure database directory exists for SQLite

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

[Add your license information here]

## Acknowledgments

- [Spleeter](https://github.com/deezer/spleeter) by Deezer for audio source separation
- [librosa](https://librosa.org/) for audio analysis
- [Flask](https://flask.palletsprojects.com/) for the web framework
