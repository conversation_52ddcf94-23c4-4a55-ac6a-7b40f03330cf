flatbuffers-1.12.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
flatbuffers-1.12.dist-info/METADATA,sha256=QyI_xr549QUA0qgYsgs8JAKAScZVbOs6CCXm9ahiZ54,872
flatbuffers-1.12.dist-info/RECORD,,
flatbuffers-1.12.dist-info/WHEEL,sha256=8zNYZbwQSXoB9IfXOjPfeNwvAsALAjffgk27FqvCWbo,110
flatbuffers-1.12.dist-info/top_level.txt,sha256=UXVWLA8ys6HeqTz6rfKesocUq6ln-ZL8mhZC_cq5BEc,12
flatbuffers/__init__.py,sha256=sK-TOKCnIp7eQIenQ_i2o-1-_icJaoQFehp9hSrij3U,717
flatbuffers/__pycache__/__init__.cpython-310.pyc,,
flatbuffers/__pycache__/builder.cpython-310.pyc,,
flatbuffers/__pycache__/compat.cpython-310.pyc,,
flatbuffers/__pycache__/encode.cpython-310.pyc,,
flatbuffers/__pycache__/number_types.cpython-310.pyc,,
flatbuffers/__pycache__/packer.cpython-310.pyc,,
flatbuffers/__pycache__/table.cpython-310.pyc,,
flatbuffers/__pycache__/util.cpython-310.pyc,,
flatbuffers/builder.py,sha256=7tVS_lhCUP3j9X3RDeIo84BDGEPPwRbidQ_fJPxo-K4,25858
flatbuffers/compat.py,sha256=PtBm1XvkbtJd-dJjDQkHi6hwdsOLui3j0VGInmhK5Nw,2342
flatbuffers/encode.py,sha256=SMrGb1y8_EccMm5vMU1PjgAhb9_NHTiFkj83kNI7dng,1581
flatbuffers/number_types.py,sha256=8IKuKo822v4gQvUOO_CDCuJfg4diGBOLOBj5kLV7dPo,3955
flatbuffers/packer.py,sha256=pw3y_NM-WOEcbfT6NzACszU9PPESQYDZabofgRxsj-g,1165
flatbuffers/table.py,sha256=wQ9EHmuVi1ie2m-1ZXopMgjauoH8bnb8b9kI4sRLuAU,4708
flatbuffers/util.py,sha256=Ib1cSx9fsgF9BY84iYl0Tjj7qftNhniI7ZEWyJwvZGE,1669
