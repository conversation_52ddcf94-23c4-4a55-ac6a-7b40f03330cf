import pytest
import tempfile
import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

try:
    from backend.app import create_app
    from backend.models.track import DatabaseManager
except ImportError:
    # Fallback for import issues
    create_app = None
    DatabaseManager = None

@pytest.fixture
def app():
    """Create application for testing."""
    if create_app is None:
        pytest.skip("Backend app not available due to import issues")

    # Create temporary directories for testing
    temp_dir = tempfile.mkdtemp()
    upload_dir = os.path.join(temp_dir, 'uploads')
    stems_dir = os.path.join(temp_dir, 'stems')
    os.makedirs(upload_dir)
    os.makedirs(stems_dir)

    # Test configuration
    test_config = {
        'TESTING': True,
        'DATABASE_URL': 'sqlite:///:memory:',
        'UPLOAD_FOLDER': upload_dir,
        'STEMS_FOLDER': stems_dir,
        'MAX_CONTENT_LENGTH': 1024 * 1024,  # 1MB for testing
        'SECRET_KEY': 'test-secret-key',
        'WTF_CSRF_ENABLED': False
    }

    try:
        app = create_app('testing')
        app.config.update(test_config)

        with app.app_context():
            yield app
    except Exception as e:
        pytest.skip(f"App creation failed: {e}")

    # Cleanup
    import shutil
    shutil.rmtree(temp_dir, ignore_errors=True)

@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()

@pytest.fixture
def db_manager(app):
    """Create database manager for testing."""
    if DatabaseManager is None:
        pytest.skip("DatabaseManager not available due to import issues")
    return DatabaseManager(app.config['DATABASE_URL'])

@pytest.fixture
def sample_audio_file():
    """Create a sample audio file for testing."""
    # This would create a minimal valid audio file
    # For now, we'll use a mock file
    import io
    return io.BytesIO(b'fake audio data')

@pytest.fixture
def sample_metadata():
    """Sample metadata for testing."""
    return {
        'duration': 180.5,
        'bpm': 120,
        'key': 'C',
        'genre': 'Pop',
        'spectral_centroid': 2500.0,
        'spectral_rolloff': 5000.0,
        'zero_crossing_rate': 0.1,
        'rms_energy': 0.05
    }
