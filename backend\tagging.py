# This file is deprecated. Use services/tagging.py instead.
# Keeping for backward compatibility during migration.

import warnings
from .services.tagging import extract_metadata as new_extract_metadata

warnings.warn(
    "tagging.py is deprecated. Use services/tagging.py instead.",
    DeprecationWarning,
    stacklevel=2
)

def extract_metadata(audio_path, stems_folder):
    """
    Legacy function for metadata extraction.
    This is deprecated - use services/tagging.py instead.
    """
    warnings.warn(
        "This extract_metadata function is deprecated. Use services.tagging.extract_metadata instead.",
        DeprecationWarning,
        stacklevel=2
    )

    return new_extract_metadata(audio_path, stems_folder)