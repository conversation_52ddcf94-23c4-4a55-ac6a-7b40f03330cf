import librosa

def extract_metadata(audio_path, stems_folder):
    y, sr = librosa.load(audio_path)
    tempo, _ = librosa.beat.beat_track(y, sr=sr)
    key = librosa.key.key_to_note(librosa.feature.chroma_cqt(y=y, sr=sr).mean(axis=1).argmax())
    
    # Placeholder genre classifier
    genre = "Unknown"
    return {
        "bpm": round(tempo),
        "key": key,
        "genre": genre,
        "stems_path": stems_folder
    }