import pytest
from backend.models.track import Track, DatabaseManager
from datetime import datetime

class TestTrackModel:
    """Test Track model."""
    
    def test_track_creation(self):
        """Test creating a track instance."""
        track = Track(
            filename='test.mp3',
            original_filename='original_test.mp3',
            file_size=1024,
            duration=180.5,
            bpm=120,
            key='C',
            genre='Pop'
        )
        
        assert track.filename == 'test.mp3'
        assert track.original_filename == 'original_test.mp3'
        assert track.file_size == 1024
        assert track.duration == 180.5
        assert track.bpm == 120
        assert track.key == 'C'
        assert track.genre == 'Pop'
        assert track.processing_status == 'pending'
    
    def test_track_to_dict(self):
        """Test track serialization to dictionary."""
        track = Track(
            id=1,
            filename='test.mp3',
            original_filename='original_test.mp3',
            file_size=1024,
            duration=180.5,
            bpm=120,
            key='C',
            genre='Pop',
            processing_status='completed'
        )
        
        track_dict = track.to_dict()
        
        assert track_dict['id'] == 1
        assert track_dict['filename'] == 'test.mp3'
        assert track_dict['original_filename'] == 'original_test.mp3'
        assert track_dict['file_size'] == 1024
        assert track_dict['duration'] == 180.5
        assert track_dict['bpm'] == 120
        assert track_dict['key'] == 'C'
        assert track_dict['genre'] == 'Pop'
        assert track_dict['processing_status'] == 'completed'
    
    def test_track_repr(self):
        """Test track string representation."""
        track = Track(filename='test.mp3')
        assert repr(track) == '<Track test.mp3>'

class TestDatabaseManager:
    """Test DatabaseManager."""
    
    def test_database_manager_creation(self):
        """Test creating database manager."""
        db_manager = DatabaseManager('sqlite:///:memory:')
        assert db_manager.engine is not None
        assert db_manager.SessionLocal is not None
    
    def test_create_tables(self):
        """Test table creation."""
        db_manager = DatabaseManager('sqlite:///:memory:')
        # Should not raise an exception
        db_manager.create_tables()
    
    def test_session_management(self):
        """Test session creation and closing."""
        db_manager = DatabaseManager('sqlite:///:memory:')
        db_manager.create_tables()
        
        session = db_manager.get_session()
        assert session is not None
        
        # Test adding a track
        track = Track(
            filename='test.mp3',
            original_filename='test.mp3'
        )
        session.add(track)
        session.commit()
        
        # Test querying
        retrieved_track = session.query(Track).first()
        assert retrieved_track is not None
        assert retrieved_track.filename == 'test.mp3'
        
        db_manager.close_session(session)
