import os
import logging
from flask import Flask, request, jsonify, abort
from flask_cors import CORS
from marshmallow import ValidationError
from contextlib import contextmanager

# Configure for CPU-only usage (suppress GPU warnings)
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

# Import CPU configuration
try:
    from config_cpu import configure_cpu_only, optimize_for_cpu
    print("✅ CPU-only configuration loaded")
except ImportError:
    print("⚠️  CPU configuration not found, using defaults")

# Import configuration
from config import config

# Import models
from models.track import Track, DatabaseManager

# Import services
from services.separation import separate_stems
from services.tagging import extract_metadata
from services.storage import FileStorageService

# Import utilities
from utils.validators import (
    validate_file_upload,
    validate_audio_metadata,
    track_response_schema,
    error_response_schema,
    success_response_schema
)

def create_app(config_name='default'):
    """Application factory pattern."""

    # Create Flask app
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(config[config_name])

    # Setup CORS
    CORS(app, origins=app.config['CORS_ORIGINS'])

    # Setup logging
    setup_logging(app)

    # Initialize database
    db_manager = DatabaseManager(app.config['DATABASE_URL'])
    db_manager.create_tables()

    # Initialize file storage service
    storage_service = FileStorageService(
        upload_folder=app.config['UPLOAD_FOLDER'],
        stems_folder=app.config['STEMS_FOLDER'],
        allowed_extensions=app.config['ALLOWED_EXTENSIONS']
    )

    @contextmanager
    def get_db_session():
        """Context manager for database sessions."""
        session = db_manager.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            db_manager.close_session(session)

    @app.errorhandler(400)
    def bad_request(error):
        """Handle bad request errors."""
        return jsonify(error_response_schema.dump({
            'error': 'Bad Request',
            'message': str(error.description),
            'status_code': 400
        })), 400

    @app.errorhandler(404)
    def not_found(error):
        """Handle not found errors."""
        return jsonify(error_response_schema.dump({
            'error': 'Not Found',
            'message': 'Resource not found',
            'status_code': 404
        })), 404

    @app.errorhandler(500)
    def internal_error(error):
        """Handle internal server errors."""
        app.logger.error(f"Internal server error: {str(error)}")
        return jsonify(error_response_schema.dump({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred',
            'status_code': 500
        })), 500

    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint."""
        return jsonify(success_response_schema.dump({
            'status': 'healthy',
            'message': 'Music processing API is running'
        }))

    @app.route('/upload', methods=['POST'])
    def upload_audio():
        """Upload and process audio file."""
        try:
            # Validate request
            if 'audio' not in request.files:
                abort(400, 'No audio file provided')

            file = request.files['audio']

            # Validate file
            validate_file_upload(file)

            # Check file size
            if request.content_length > app.config['MAX_CONTENT_LENGTH']:
                abort(400, f'File too large. Maximum size: {app.config["MAX_CONTENT_LENGTH"]} bytes')

            # Save uploaded file
            try:
                secure_filename, file_path = storage_service.save_uploaded_file(file)
                file_size = storage_service.get_file_size(file_path)

                app.logger.info(f"File uploaded: {secure_filename} ({file_size} bytes)")

            except ValueError as e:
                abort(400, str(e))
            except Exception as e:
                app.logger.error(f"File upload failed: {str(e)}")
                abort(500, 'File upload failed')

            # Create track record
            with get_db_session() as session:
                track = Track(
                    filename=secure_filename,
                    original_filename=file.filename,
                    file_size=file_size,
                    processing_status='processing'
                )
                session.add(track)
                session.flush()  # Get the ID
                track_id = track.id

            try:
                # Process audio file
                app.logger.info(f"Starting audio processing for track {track_id}")

                # Separate stems
                stems_dir = separate_stems(file_path)

                # Extract metadata
                metadata = extract_metadata(file_path, stems_dir)
                validate_audio_metadata(metadata)

                # Update track with results
                with get_db_session() as session:
                    track = session.query(Track).get(track_id)
                    if track:
                        track.duration = metadata.get('duration')
                        track.bpm = metadata.get('bpm')
                        track.key = metadata.get('key')
                        track.genre = metadata.get('genre')
                        track.stems_path = stems_dir
                        track.processing_status = 'completed'

                        # Store additional metadata as needed
                        session.flush()

                        response_data = track_response_schema.dump(track.to_dict())

                app.logger.info(f"Audio processing completed for track {track_id}")

                return jsonify(success_response_schema.dump({
                    'status': 'success',
                    'message': 'Audio processed successfully',
                    'data': response_data
                }))

            except Exception as e:
                # Update track status to failed
                try:
                    with get_db_session() as session:
                        track = session.query(Track).get(track_id)
                        if track:
                            track.processing_status = 'failed'
                except Exception:
                    pass  # Don't fail if we can't update status

                app.logger.error(f"Audio processing failed for track {track_id}: {str(e)}")
                abort(500, f'Audio processing failed: {str(e)}')

        except ValidationError as e:
            abort(400, f'Validation error: {e.messages}')
        except Exception as e:
            app.logger.error(f"Unexpected error in upload_audio: {str(e)}")
            abort(500, 'An unexpected error occurred')

    return app

def setup_logging(app):
    """Setup application logging."""
    if not app.debug:
        # Setup file logging for production
        if not os.path.exists('logs'):
            os.mkdir('logs')

        file_handler = logging.FileHandler(app.config.get('LOG_FILE', 'logs/app.log'))
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        app.logger.addHandler(file_handler)

        app.logger.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        app.logger.info('Music processing API startup')

# Create app instance
app = create_app(os.environ.get('FLASK_ENV', 'default'))

if __name__ == '__main__':
    app.run(
        debug=app.config['DEBUG'],
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000))
    )