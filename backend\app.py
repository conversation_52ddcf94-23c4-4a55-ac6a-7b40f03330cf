from flask import Flask, request, jsonify
from separation import separate_stems
from tagging import extract_metadata
from database import save_track

app = Flask(__name__)

@app.route('/upload', methods=['POST'])
def upload_audio():
    file = request.files['audio']
    file_path = f'uploads/{file.filename}'
    file.save(file_path)

    stems = separate_stems(file_path)
    metadata = extract_metadata(file_path, stems)
    save_track(file.filename, metadata, stems)

    return jsonify({"status": "success", "metadata": metadata})

if __name__ == '__main__':
    app.run(debug=True)