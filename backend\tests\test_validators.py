import pytest
from marshmallow import ValidationError
from unittest.mock import MagicMock
from backend.utils.validators import (
    validate_file_upload,
    validate_audio_metadata,
    track_response_schema,
    error_response_schema,
    success_response_schema
)

class TestFileValidation:
    """Test file upload validation."""
    
    def test_validate_file_upload_success(self):
        """Test successful file validation."""
        mock_file = MagicMock()
        mock_file.filename = 'test.mp3'
        
        result = validate_file_upload(mock_file)
        assert result['valid'] is True
    
    def test_validate_file_upload_no_file(self):
        """Test validation with no file."""
        with pytest.raises(ValidationError) as exc_info:
            validate_file_upload(None)
        
        assert 'No file provided' in str(exc_info.value)
    
    def test_validate_file_upload_no_filename(self):
        """Test validation with no filename."""
        mock_file = MagicMock()
        mock_file.filename = None
        
        with pytest.raises(ValidationError) as exc_info:
            validate_file_upload(mock_file)
        
        assert 'No filename provided' in str(exc_info.value)
    
    def test_validate_file_upload_empty_filename(self):
        """Test validation with empty filename."""
        mock_file = MagicMock()
        mock_file.filename = ''

        with pytest.raises(ValidationError) as exc_info:
            validate_file_upload(mock_file)

        # The validator treats empty string as "No filename provided"
        assert 'Empty filename' in str(exc_info.value) or 'No filename provided' in str(exc_info.value)

class TestMetadataValidation:
    """Test audio metadata validation."""
    
    def test_validate_metadata_success(self):
        """Test successful metadata validation."""
        metadata = {
            'duration': 180.5,
            'bpm': 120,
            'key': 'C',
            'genre': 'Pop'
        }
        
        result = validate_audio_metadata(metadata)
        assert result == metadata
    
    def test_validate_metadata_no_duration(self):
        """Test validation without duration."""
        metadata = {
            'bpm': 120,
            'key': 'C'
        }
        
        with pytest.raises(ValidationError) as exc_info:
            validate_audio_metadata(metadata)
        
        assert 'Duration is required' in str(exc_info.value)
    
    def test_validate_metadata_invalid_duration(self):
        """Test validation with invalid duration."""
        metadata = {
            'duration': -10,
            'bpm': 120
        }
        
        with pytest.raises(ValidationError) as exc_info:
            validate_audio_metadata(metadata)
        
        assert 'Duration must be a positive number' in str(exc_info.value)
    
    def test_validate_metadata_invalid_bpm(self):
        """Test validation with invalid BPM."""
        metadata = {
            'duration': 180.5,
            'bpm': -50
        }
        
        with pytest.raises(ValidationError) as exc_info:
            validate_audio_metadata(metadata)
        
        assert 'BPM must be a positive number' in str(exc_info.value)
    
    def test_validate_metadata_invalid_key(self):
        """Test validation with invalid key."""
        metadata = {
            'duration': 180.5,
            'key': 'X'  # Invalid key
        }
        
        with pytest.raises(ValidationError) as exc_info:
            validate_audio_metadata(metadata)
        
        assert 'Invalid key' in str(exc_info.value)

class TestSchemas:
    """Test marshmallow schemas."""
    
    def test_track_response_schema(self):
        """Test track response schema."""
        from datetime import datetime

        track_data = {
            'id': 1,
            'filename': 'test.mp3',
            'original_filename': 'original_test.mp3',
            'file_size': 1024,
            'duration': 180.5,
            'bpm': 120,
            'key': 'C',
            'genre': 'Pop',
            'stems_path': 'stems/test',
            'processing_status': 'completed',
            'created_at': datetime(2024, 1, 1, 12, 0, 0),
            'updated_at': datetime(2024, 1, 1, 12, 0, 0)
        }
        
        result = track_response_schema.dump(track_data)
        assert result['id'] == 1
        assert result['filename'] == 'test.mp3'
        assert result['processing_status'] == 'completed'
    
    def test_error_response_schema(self):
        """Test error response schema."""
        error_data = {
            'error': 'Bad Request',
            'message': 'Invalid file format',
            'status_code': 400
        }
        
        result = error_response_schema.dump(error_data)
        assert result['error'] == 'Bad Request'
        assert result['message'] == 'Invalid file format'
        assert result['status_code'] == 400
    
    def test_success_response_schema(self):
        """Test success response schema."""
        success_data = {
            'status': 'success',
            'message': 'File processed successfully',
            'data': {'id': 1, 'filename': 'test.mp3'}
        }
        
        result = success_response_schema.dump(success_data)
        assert result['status'] == 'success'
        assert result['message'] == 'File processed successfully'
        assert result['data']['id'] == 1
