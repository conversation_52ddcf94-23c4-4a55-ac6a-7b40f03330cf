from marshmallow import Schema, fields, validate, ValidationError
from typing import Dict, Any

class AudioUploadSchema(Schema):
    """Schema for validating audio upload requests."""
    
    # File validation will be handled separately since marshmallow doesn't handle file uploads directly
    # This schema is for any additional parameters that might be sent with the upload
    
    class Meta:
        strict = True

class TrackResponseSchema(Schema):
    """Schema for track response serialization."""
    
    id = fields.Integer(required=True)
    filename = fields.String(required=True)
    original_filename = fields.String(required=True)
    file_size = fields.Integer(allow_none=True)
    duration = fields.Float(allow_none=True)
    bpm = fields.Integer(allow_none=True)
    key = fields.String(allow_none=True)
    genre = fields.String(allow_none=True)
    stems_path = fields.String(allow_none=True)
    processing_status = fields.String(required=True)
    created_at = fields.DateTime(allow_none=True)
    updated_at = fields.DateTime(allow_none=True)
    
    # Additional metadata fields
    spectral_centroid = fields.Float(allow_none=True)
    spectral_rolloff = fields.Float(allow_none=True)
    zero_crossing_rate = fields.Float(allow_none=True)
    rms_energy = fields.Float(allow_none=True)

class ErrorResponseSchema(Schema):
    """Schema for error responses."""
    
    error = fields.String(required=True)
    message = fields.String(required=True)
    status_code = fields.Integer(required=True)

class SuccessResponseSchema(Schema):
    """Schema for success responses."""
    
    status = fields.String(required=True)
    message = fields.String(allow_none=True)
    data = fields.Raw(allow_none=True)

def validate_file_upload(file) -> Dict[str, Any]:
    """
    Validate uploaded file.
    
    Args:
        file: Uploaded file object
        
    Returns:
        Dictionary with validation results
        
    Raises:
        ValidationError: If validation fails
    """
    errors = []
    
    if not file:
        errors.append("No file provided")
    elif not file.filename:
        errors.append("No filename provided")
    elif file.filename == '':
        errors.append("Empty filename")
    
    if errors:
        raise ValidationError(errors)
    
    return {"valid": True}

def validate_audio_metadata(metadata: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate extracted audio metadata.
    
    Args:
        metadata: Metadata dictionary
        
    Returns:
        Validated metadata dictionary
        
    Raises:
        ValidationError: If validation fails
    """
    errors = []
    
    # Check required fields
    if 'duration' not in metadata:
        errors.append("Duration is required")
    elif not isinstance(metadata['duration'], (int, float)) or metadata['duration'] <= 0:
        errors.append("Duration must be a positive number")
    
    # Validate BPM if present
    if 'bpm' in metadata and metadata['bpm'] is not None:
        if not isinstance(metadata['bpm'], (int, float)) or metadata['bpm'] <= 0:
            errors.append("BPM must be a positive number")
    
    # Validate key if present
    if 'key' in metadata and metadata['key'] is not None:
        valid_keys = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
        if metadata['key'] not in valid_keys:
            errors.append(f"Invalid key. Must be one of: {valid_keys}")
    
    if errors:
        raise ValidationError(errors)
    
    return metadata

# Schema instances for reuse
audio_upload_schema = AudioUploadSchema()
track_response_schema = TrackResponseSchema()
error_response_schema = ErrorResponseSchema()
success_response_schema = SuccessResponseSchema()
