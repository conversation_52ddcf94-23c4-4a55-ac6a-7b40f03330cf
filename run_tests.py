#!/usr/bin/env python3
"""
Test runner for Music Processing API
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        if e.stderr:
            print(f"Error: {e.stderr}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        return False

def check_dependencies():
    """Check if required dependencies are installed."""
    print("🔍 Checking test dependencies...")
    
    required_packages = [
        ('pytest', 'pytest'),
        ('backend.models.track', 'Backend models'),
        ('backend.utils.validators', 'Backend validators'),
        ('flask', 'Flask'),
        ('marshmallow', 'Marshmallow')
    ]
    
    missing = []
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✅ {name} available")
        except ImportError:
            print(f"❌ {name} missing")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️  Missing dependencies: {', '.join(missing)}")
        print("Install with: pip install pytest marshmallow")
        if 'flask' in missing:
            print("Also run: python fix_dependencies.py")
        return False
    
    return True

def run_tests():
    """Run the test suite."""
    print("\n🧪 Running Test Suite")
    print("=" * 30)

    # Ensure we're in the right directory
    if not os.path.exists('backend/tests'):
        print("❌ Test directory not found. Make sure you're in the project root.")
        return False

    # Add current directory to Python path
    current_dir = os.getcwd()
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
        print(f"✅ Added {current_dir} to Python path")

    # Set PYTHONPATH environment variable
    os.environ['PYTHONPATH'] = current_dir
    print(f"✅ Set PYTHONPATH to {current_dir}")
    
    # Create required directories
    os.makedirs('db', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('stems', exist_ok=True)
    
    test_commands = [
        # Test individual components
        ("python -m pytest backend/tests/test_models.py -v", "Testing models"),
        ("python -m pytest backend/tests/test_validators.py -v", "Testing validators"),
        ("python -m pytest backend/tests/test_app.py -v", "Testing Flask app"),

        # Run all tests
        ("python -m pytest backend/tests/ -v", "Running all tests"),

        # Generate coverage report
        ("python -m pytest backend/tests/ --cov=backend --cov-report=term-missing", "Generating coverage report")
    ]
    
    results = []
    for command, description in test_commands:
        success = run_command(command, description)
        results.append((description, success))
        print("-" * 50)
    
    # Summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    
    passed = 0
    for description, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {description}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} test suites passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Your setup is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

def main():
    """Main function."""
    print("🎵 Music Processing API - Test Runner")
    print("=" * 45)
    
    # Check dependencies
    if not check_dependencies():
        print("\n💡 To install missing dependencies:")
        print("pip install pytest pytest-cov marshmallow")
        print("python fix_dependencies.py")
        return
    
    # Run tests
    if run_tests():
        print("\n🚀 Next steps:")
        print("1. Start the app: python start_cpu.py")
        print("2. Test the API: curl http://localhost:5000/health")
    else:
        print("\n🔧 Troubleshooting:")
        print("1. Check that all dependencies are installed")
        print("2. Run: python fix_dependencies.py")
        print("3. Try individual test files: pytest backend/tests/test_models.py -v")

if __name__ == "__main__":
    main()
