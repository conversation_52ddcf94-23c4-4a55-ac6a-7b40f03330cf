from sqlalchemy import create_engine, Column, Integer, String, DateTime, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

Base = declarative_base()

class Track(Base):
    """Track model for storing audio file metadata and processing results."""
    
    __tablename__ = 'tracks'
    
    id = Column(Integer, primary_key=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_size = Column(Integer)  # File size in bytes
    duration = Column(Float)  # Duration in seconds
    
    # Audio metadata
    bpm = Column(Integer)
    key = Column(String(10))
    genre = Column(String(50))
    
    # Processing results
    stems_path = Column(String(500))
    processing_status = Column(String(20), default='pending', server_default='pending')  # pending, processing, completed, failed
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if self.processing_status is None:
            self.processing_status = 'pending'

    def __repr__(self):
        return f'<Track {self.filename}>'
    
    def to_dict(self):
        """Convert track to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'duration': self.duration,
            'bpm': self.bpm,
            'key': self.key,
            'genre': self.genre,
            'stems_path': self.stems_path,
            'processing_status': self.processing_status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class DatabaseManager:
    """Database manager for handling connections and sessions."""
    
    def __init__(self, database_url):
        self.engine = create_engine(database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
    def create_tables(self):
        """Create all tables."""
        Base.metadata.create_all(self.engine)
        
    def get_session(self):
        """Get a new database session."""
        return self.SessionLocal()
        
    def close_session(self, session):
        """Close a database session."""
        session.close()
