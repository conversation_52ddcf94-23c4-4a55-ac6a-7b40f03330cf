# This file is deprecated. Use services/separation.py instead.
# Keeping for backward compatibility during migration.

import warnings
from .services.separation import separate_stems as new_separate_stems

warnings.warn(
    "separation.py is deprecated. Use services/separation.py instead.",
    DeprecationWarning,
    stacklevel=2
)

def separate_stems(audio_path):
    """
    Legacy function for stem separation.
    This is deprecated - use services/separation.py instead.
    """
    warnings.warn(
        "This separate_stems function is deprecated. Use services.separation.separate_stems instead.",
        DeprecationWarning,
        stacklevel=2
    )

    return new_separate_stems(audio_path)