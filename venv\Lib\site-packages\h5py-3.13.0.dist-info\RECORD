h5py-3.13.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
h5py-3.13.0.dist-info/LICENSE,sha256=t3O8fPA4Y3p_2Ah2SMwo4F8PJNU3cNGCcUekCh0mirU,1550
h5py-3.13.0.dist-info/METADATA,sha256=F1n3M5lQQKUIXDTDpUt7-Cb-OOUGf2gjVq-xL-UtoNk,2543
h5py-3.13.0.dist-info/RECORD,,
h5py-3.13.0.dist-info/WHEEL,sha256=rzGfZgUcGeKSgIHGYMuqg4xE4VPHxnaldXH6BG0zjVk,101
h5py-3.13.0.dist-info/top_level.txt,sha256=fO7Bsaa0F3Nx6djErCCbSw4-E7rBFMWrBVTGLEMxUMg,5
h5py/__init__.py,sha256=_rrHccfmxYOX14DlmFP9jyloa1IUTlg2FhuRIXN27ZE,3777
h5py/__pycache__/__init__.cpython-310.pyc,,
h5py/__pycache__/h5py_warnings.cpython-310.pyc,,
h5py/__pycache__/ipy_completer.cpython-310.pyc,,
h5py/__pycache__/version.cpython-310.pyc,,
h5py/_conv.cp310-win_amd64.pyd,sha256=rSjOchYtJoL4GqXUTz2BidbI-vy7y6iGrJLtpiyqJLM,181760
h5py/_errors.cp310-win_amd64.pyd,sha256=QJtquG5VN7g_LH5SMmDx5t0Ol964sbGB5EJtCzMhLCE,50688
h5py/_hl/__init__.py,sha256=McVVGnMUXmMKA8GWPD9ie2jqe9qsgZ0rSYKmQIEN7C0,472
h5py/_hl/__pycache__/__init__.cpython-310.pyc,,
h5py/_hl/__pycache__/attrs.cpython-310.pyc,,
h5py/_hl/__pycache__/base.cpython-310.pyc,,
h5py/_hl/__pycache__/compat.cpython-310.pyc,,
h5py/_hl/__pycache__/dataset.cpython-310.pyc,,
h5py/_hl/__pycache__/datatype.cpython-310.pyc,,
h5py/_hl/__pycache__/dims.cpython-310.pyc,,
h5py/_hl/__pycache__/files.cpython-310.pyc,,
h5py/_hl/__pycache__/filters.cpython-310.pyc,,
h5py/_hl/__pycache__/group.cpython-310.pyc,,
h5py/_hl/__pycache__/selections.cpython-310.pyc,,
h5py/_hl/__pycache__/selections2.cpython-310.pyc,,
h5py/_hl/__pycache__/vds.cpython-310.pyc,,
h5py/_hl/attrs.py,sha256=aKR8KCrno5KmUPifq1s_IxcpJGq0Ut02lEVGx90Ug2M,10499
h5py/_hl/base.py,sha256=2ZUhKX8yqaJvKI4fW2by7VQjQOnVxNImRbg7E3pHvLI,16159
h5py/_hl/compat.py,sha256=KSn38wDu5KOSEDImaOidHizTr0P-5eQwDahXzfspYaA,1654
h5py/_hl/dataset.py,sha256=fxX6gN_U1L6PfeGRM8p9i5UxKFjJiOhQ1kkC15EJFM4,44107
h5py/_hl/datatype.py,sha256=CKWF_DmX_A9afqf3anXcs0b-yC2p8_mcPIqS3alZB5U,1603
h5py/_hl/dims.py,sha256=aBBk4muIacpLhl6ra97wZx0v9U75KMBC9yqjm8Ajzq4,5290
h5py/_hl/files.py,sha256=RuOVdG7tLdpOMqDToyj3n-tJdVwd1DSBqOH_2xqEg2E,26051
h5py/_hl/filters.py,sha256=vW-spqNhHpIal7G9hC-wWDdYl9XxjDEM_KkkpQ-_oZA,14988
h5py/_hl/group.py,sha256=Iq8VNoiSwxm2pN_ntKk2INEgrYgl94P0kIPtSa2bPOQ,31412
h5py/_hl/selections.py,sha256=oLobyA7ITnNs7-EMAnuFcvJRlddPdWBoL86VDYhHmtA,14910
h5py/_hl/selections2.py,sha256=BSuUwex1Qfy1wN2Wb3dAD2vpnb8KXEoJRvLKG0GoAaI,2826
h5py/_hl/vds.py,sha256=9gM-jnGP-Ew4PkChaFUxhuLd9M78sIAbP6o6WEtekmU,9629
h5py/_objects.cp310-win_amd64.pyd,sha256=-rnaBor82eu0BDfP6Gh0oDMOnHU_yVoUFnCYdaZI4fQ,111616
h5py/_proxy.cp310-win_amd64.pyd,sha256=iA8oSKm2yo6VnlCCbxWdRDZR4syYbFAhIRNLnFjpRHY,38400
h5py/_selector.cp310-win_amd64.pyd,sha256=Cmm5_5Oz32Nv9QblkUKH4j9wv9ZULe5detL4CIh__e0,137216
h5py/defs.cp310-win_amd64.pyd,sha256=iqlIOrkE5OO9TKYiGrHdtUnZEBtePh9ELKkW0p0pW6M,219136
h5py/h5.cp310-win_amd64.pyd,sha256=ZRGrT6pIiTEoXtyYZiYk0wx5FofWCJZuGDesvrok69c,90112
h5py/h5a.cp310-win_amd64.pyd,sha256=PYfeTXNdPnCq0fywbh3mbQ09W0qClbVT0GqoIC0kLrA,125952
h5py/h5ac.cp310-win_amd64.pyd,sha256=xOjY5nGdvXewtONKM_qsoXVH7yJgAJFlN48FIHoWM90,52736
h5py/h5d.cp310-win_amd64.pyd,sha256=ayXw4XZcbJqlVPbIFvoZMZENE6X3zgtNau5I12oLbIk,253440
h5py/h5ds.cp310-win_amd64.pyd,sha256=xmsGeH2_lK4Hy4VzwBuwQPR0uXLsBcaLUxl8DNTURWc,77824
h5py/h5f.cp310-win_amd64.pyd,sha256=Ya-TN2AhRhIYc0eXgdwJmLUIEaNkHIMC4QEyNmh3ZJg,141824
h5py/h5fd.cp310-win_amd64.pyd,sha256=f_ikqZvkpa7--qaxzw7p10bjPogHpsn2ml3q6lagvIU,151040
h5py/h5g.cp310-win_amd64.pyd,sha256=jcbD975TcVWSCXU0fHQh3hf_xKvvy9UyUFUkr0Anpyo,154624
h5py/h5i.cp310-win_amd64.pyd,sha256=MDKkL0p9ETqGz95W61rBhxbKaeQwnU-hZcHNoogQGZ8,58368
h5py/h5l.cp310-win_amd64.pyd,sha256=JoKncux-LJSky1CISNZEgKXZYnrkWKIcr92gojdbuxw,104960
h5py/h5o.cp310-win_amd64.pyd,sha256=rqqR0HxzJP3HYgoJbhjeop_MBCmVtFtDNVQUZu95DG4,123904
h5py/h5p.cp310-win_amd64.pyd,sha256=rc80ILJP-IrOMzgDn-N9QWkK4QR_Ms4FyOdfV0vGXcg,401408
h5py/h5pl.cp310-win_amd64.pyd,sha256=n7jp9TvnB_37_-kYLFjx-wyfkQs8Re7FhM4EeBfziK0,47104
h5py/h5py_warnings.py,sha256=YR2DLMumy_qgchAg5_MLJniloT90Dil5_OHftF7MmD8,544
h5py/h5r.cp310-win_amd64.pyd,sha256=wXPMR--3-6bszO9moixuh1aS5TI_ACv0RRcjF6JIAbM,68096
h5py/h5s.cp310-win_amd64.pyd,sha256=OmTegTBWJ67Ru2vt55fMpLQz8vqLwYoCNUsdaAWvtZQ,124416
h5py/h5t.cp310-win_amd64.pyd,sha256=l9ly7j4WLjy58ZLN5lIMR9rmjqlwRzhU01x1X_QWkIk,369664
h5py/h5z.cp310-win_amd64.pyd,sha256=0OHShfAh7pNofvr56Q-zrQbljjmLcit21eiVGFDy1IE,52224
h5py/hdf5.dll,sha256=YNFb_4U6DqLwUGg4UzKalivLPBTZHdL9q0oCojaZd4A,3330560
h5py/hdf5_hl.dll,sha256=IMRg7KwlPGGC3zmkYsRpSv5IYAJtIqDqspCxj5GR3jw,121344
h5py/ipy_completer.py,sha256=iOKbnKizsvTlbsVU4ArkZHLWLqJfbjsrKMfLKYgQSGo,3889
h5py/tests/__init__.py,sha256=fLCW7Dm0BboDAVY-KDWdK_GRPPJv-KW7Z31kiKOaCtI,691
h5py/tests/__pycache__/__init__.cpython-310.pyc,,
h5py/tests/__pycache__/common.cpython-310.pyc,,
h5py/tests/__pycache__/conftest.cpython-310.pyc,,
h5py/tests/__pycache__/test_attribute_create.cpython-310.pyc,,
h5py/tests/__pycache__/test_attrs.cpython-310.pyc,,
h5py/tests/__pycache__/test_attrs_data.cpython-310.pyc,,
h5py/tests/__pycache__/test_base.cpython-310.pyc,,
h5py/tests/__pycache__/test_big_endian_file.cpython-310.pyc,,
h5py/tests/__pycache__/test_completions.cpython-310.pyc,,
h5py/tests/__pycache__/test_dataset.cpython-310.pyc,,
h5py/tests/__pycache__/test_dataset_getitem.cpython-310.pyc,,
h5py/tests/__pycache__/test_dataset_swmr.cpython-310.pyc,,
h5py/tests/__pycache__/test_datatype.cpython-310.pyc,,
h5py/tests/__pycache__/test_dimension_scales.cpython-310.pyc,,
h5py/tests/__pycache__/test_dims_dimensionproxy.cpython-310.pyc,,
h5py/tests/__pycache__/test_dtype.cpython-310.pyc,,
h5py/tests/__pycache__/test_errors.cpython-310.pyc,,
h5py/tests/__pycache__/test_file.cpython-310.pyc,,
h5py/tests/__pycache__/test_file2.cpython-310.pyc,,
h5py/tests/__pycache__/test_file_alignment.cpython-310.pyc,,
h5py/tests/__pycache__/test_file_image.cpython-310.pyc,,
h5py/tests/__pycache__/test_filters.cpython-310.pyc,,
h5py/tests/__pycache__/test_group.cpython-310.pyc,,
h5py/tests/__pycache__/test_h5.cpython-310.pyc,,
h5py/tests/__pycache__/test_h5d_direct_chunk.cpython-310.pyc,,
h5py/tests/__pycache__/test_h5f.cpython-310.pyc,,
h5py/tests/__pycache__/test_h5o.cpython-310.pyc,,
h5py/tests/__pycache__/test_h5p.cpython-310.pyc,,
h5py/tests/__pycache__/test_h5pl.cpython-310.pyc,,
h5py/tests/__pycache__/test_h5s.cpython-310.pyc,,
h5py/tests/__pycache__/test_h5t.cpython-310.pyc,,
h5py/tests/__pycache__/test_h5z.cpython-310.pyc,,
h5py/tests/__pycache__/test_objects.cpython-310.pyc,,
h5py/tests/__pycache__/test_ros3.cpython-310.pyc,,
h5py/tests/__pycache__/test_selections.cpython-310.pyc,,
h5py/tests/__pycache__/test_slicing.cpython-310.pyc,,
h5py/tests/common.py,sha256=exoIAp_l0nZhZLXZgO9wGTsN5ys23gpj1QffpREwXIg,8006
h5py/tests/conftest.py,sha256=OiQZ2XuYVfeikYVoHGIuOkgXgsaKPpbTpfB6rY07eoA,569
h5py/tests/data_files/__init__.py,sha256=mqVluyvrAZ6HEZH0QAX8D8q-ErxJG_b3roDk1UMh_RU,200
h5py/tests/data_files/__pycache__/__init__.cpython-310.pyc,,
h5py/tests/data_files/vlen_string_dset.h5,sha256=kA-LrxnT2MTRGTrBrGAZ7nd0AF6FwDCvROZ1ezjSl5M,6304
h5py/tests/data_files/vlen_string_dset_utc.h5,sha256=hbcoOCuDPB2mFie5ozTiKCLVwcs1n-O6byUmKvRTL2M,169904
h5py/tests/data_files/vlen_string_s390x.h5,sha256=6pkMaOA3G6-rSWb3UvoTVbO59kNgBm9tna_48bfnTKU,9008
h5py/tests/test_attribute_create.py,sha256=liDSJwp2td9I6jsEV0xXc6YNdpK4U2NU4zh8cDfQHYA,3085
h5py/tests/test_attrs.py,sha256=fLUZ7E-xsgh6Z8RLKn-yabUMb2ojt9ijM_rWHjD8M-E,9696
h5py/tests/test_attrs_data.py,sha256=gSUOwHyBwCTmROTP6jiBdUEnssipWindpfyO378unBc,10079
h5py/tests/test_base.py,sha256=QTxwpyHIJaT1NXEV95ApbEikEtzh5ZqMfD6bSNTUuSw,3962
h5py/tests/test_big_endian_file.py,sha256=oPoetdkfWeGLSAMpEGagpRQC7VbIZplkU9sLKyxR0OM,1497
h5py/tests/test_completions.py,sha256=jA2OuWaYhcPr4fAi04j6D3DjgCdY-BIzJquCQHa2Hvo,1525
h5py/tests/test_dataset.py,sha256=27zVxh7zD6XTOydsYZMLY9Q09QdoDW0IwPl35YeyDfc,82614
h5py/tests/test_dataset_getitem.py,sha256=FLWDesUVsY-SsQsnKOY_3gaIhP0znUJbUryHXkgiGU0,19339
h5py/tests/test_dataset_swmr.py,sha256=XDzSDzCKIXZnoGkRUnin3hjU4nPfIiyJuBBIBj1bMxk,4093
h5py/tests/test_datatype.py,sha256=Ed8zcEv-0UahPxM4fbF3SVKHdZbrUscOTOM1MVb0Fgw,1047
h5py/tests/test_dimension_scales.py,sha256=ydkP35UlA6RAjvuqZQe2utP1weWZcTvlnwNn4rm3Rec,8334
h5py/tests/test_dims_dimensionproxy.py,sha256=mZ008MxVRhtf3WCR_pwLn527VU2NVo-i1WDYAGTF5BI,625
h5py/tests/test_dtype.py,sha256=wqvYS5HxmdbfEwonZBq1KJaIMjbE3RIwPeL5lYIaoyQ,18433
h5py/tests/test_errors.py,sha256=eA68-lYML0_CJNHNB6uH6OuZeITEOXUQ7JYoZ9DmVCI,2331
h5py/tests/test_file.py,sha256=cnMpNMh3EYQr1YCgDNSp1MwguvBvEBi88fFXn5DJbi4,35510
h5py/tests/test_file2.py,sha256=MZZRKtjDRrljByYvhDXJBWV5-WkMGhmtxvpx5wsd0Tk,11020
h5py/tests/test_file_alignment.py,sha256=aA5fB1dzJwyzA5aEagJq8NzxnUBJGvG4grc2JUc_xEo,4509
h5py/tests/test_file_image.py,sha256=f_4zTwZ1YMD3OU7XURcPWA6VasylORPkw5QuydOSQWk,2067
h5py/tests/test_filters.py,sha256=SJiYJi9wxN0C34WlQXvEEbxzv_Dxf1Dj9lGOmU0hyt0,3161
h5py/tests/test_group.py,sha256=QPBihoi5TgM0XnPYmU5DPyhM-gmYOe2JfS9PdgdhLoQ,37660
h5py/tests/test_h5.py,sha256=tUVpBOgKSVHLlDpu3urVcmaBPMt0vxAr46nIEgL4an4,1261
h5py/tests/test_h5d_direct_chunk.py,sha256=0Viao2ohJn-0MnwAinOyMhScSmosQ9e1Lr9Shohd4cg,7931
h5py/tests/test_h5f.py,sha256=iQGz7YI01xUDBAsFC7ZwZmx6hb7_Q97Zf0cfdnKjZZ4,4127
h5py/tests/test_h5o.py,sha256=j5cc1FGgSPoQgpxXC-ETSzQ32nEFuZDmKfovzjuk7Mc,529
h5py/tests/test_h5p.py,sha256=dcpP0FPpsXzTXJNm8nMoWw647MPVwExlgoDzQVlQaOM,7042
h5py/tests/test_h5pl.py,sha256=qtcjqJy99yo3GWmrRcJUFf5Pl_2rPNNpPkl-SrfnuEM,1859
h5py/tests/test_h5s.py,sha256=835CjwaM8WEckbJvqaShwAB4icFZw1sBlsWTRVsAp-g,758
h5py/tests/test_h5t.py,sha256=Uhg1w6WfOkPWH0iUBfO5Onlw2O61sUiYvsL0MixSwrs,6770
h5py/tests/test_h5z.py,sha256=2w_IFh8o2B2kIlXXMq4uc9aEppWUwkAGJX80ogs0Rbs,2004
h5py/tests/test_objects.py,sha256=MM_-y3vTepZTael-TSNMcvENb0RldXJoj1oztQ48BPM,947
h5py/tests/test_ros3.py,sha256=ttNSNiiZTvv66Fi6rdTP6c4YVlqxfPj2Njs-806hWE0,2234
h5py/tests/test_selections.py,sha256=O4ZY5XX5oJDzQayTtgD-f3Hn7kMlZZKUFRVX4RDU42s,4995
h5py/tests/test_slicing.py,sha256=8OyyTYkOAycRJbJsCjJ5_GDbdDKT4kVDjdwocD66nZY,14290
h5py/tests/test_vds/__init__.py,sha256=I2uUocf1v8DxK1ox18-rp4bt1rWkCFXLL32K6rtGZcU,107
h5py/tests/test_vds/__pycache__/__init__.cpython-310.pyc,,
h5py/tests/test_vds/__pycache__/test_highlevel_vds.cpython-310.pyc,,
h5py/tests/test_vds/__pycache__/test_lowlevel_vds.cpython-310.pyc,,
h5py/tests/test_vds/__pycache__/test_virtual_source.cpython-310.pyc,,
h5py/tests/test_vds/test_highlevel_vds.py,sha256=Z1NRsL8M0EkIDu7bcXip8zF6y1Fc84bSBjrsbFDa0ZE,17727
h5py/tests/test_vds/test_lowlevel_vds.py,sha256=Z5M8YCAIUyu6-cPcsiOL65UT_oD7zuMVuFgBhiFqSmo,12163
h5py/tests/test_vds/test_virtual_source.py,sha256=YF-uoptLTweL0gAKQaWhBNAgt2DVWH9MetPTgC6Bedw,6199
h5py/utils.cp310-win_amd64.pyd,sha256=8BYIu_JtslAqPyoY5qB4n0nutnVUyzP5CyJ7hy_AKhc,56832
h5py/version.py,sha256=csFeiTu_aGNJcugVgpnu_smtKJCwZo7zIEf_GMCy59c,1976
h5py/zlib.dll,sha256=jgKgB_0sD64jXgwuQF1YO9srBZFi6hotwpe2kLt8N8I,86016
