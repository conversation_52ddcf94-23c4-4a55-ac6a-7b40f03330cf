"""
CPU-optimized configuration for systems without GPU
"""

import os
import warnings

# Suppress TensorFlow GPU warnings
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # Suppress INFO and WARNING logs
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # Force CPU usage

# Suppress other common warnings
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

def configure_cpu_only():
    """Configure TensorFlow for CPU-only usage."""
    try:
        import tensorflow as tf
        
        # Configure TensorFlow to use CPU only
        tf.config.set_visible_devices([], 'GPU')
        
        # Set memory growth to avoid allocation issues
        physical_devices = tf.config.list_physical_devices('CPU')
        if physical_devices:
            print("✅ TensorFlow configured for CPU-only usage")
        
    except ImportError:
        # TensorFlow not installed yet, that's okay
        pass
    except Exception as e:
        print(f"⚠️  TensorFlow configuration warning: {e}")

def optimize_for_cpu():
    """Optimize various libraries for CPU usage."""
    
    # Configure NumPy for better CPU performance
    try:
        import numpy as np
        # Use all available CPU cores for NumPy operations
        os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())
        os.environ['MKL_NUM_THREADS'] = str(os.cpu_count())
        print(f"✅ NumPy configured to use {os.cpu_count()} CPU cores")
    except ImportError:
        pass
    
    # Configure librosa for CPU
    try:
        import librosa
        # Librosa will automatically use CPU
        print("✅ Librosa configured for CPU usage")
    except ImportError:
        pass

# Apply CPU optimizations when this module is imported
configure_cpu_only()
optimize_for_cpu()
